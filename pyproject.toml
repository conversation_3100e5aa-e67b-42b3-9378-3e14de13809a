[project]
name = "llm-tt"
version = "0.1.0"
description = "LLM learning project for Tong Tong"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "baidusearch>=1.0.3",
    "ipykernel>=6.29.5",
    "loguru>=0.7.3",
    "mcp[cli]>=1.7.1",
    "mkdocs-include-markdown-plugin>=7.1.5",
    "mkdocs-material>=9.6.14",
    "mkdocstrings[python]>=0.29.1",
    "openai>=1.76.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mymanus"]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
