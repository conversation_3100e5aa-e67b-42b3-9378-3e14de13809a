# 部署指南

本文档介绍如何部署智能搜索系统到不同环境。

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd intelligent-search-system

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入必要的API密钥
OPENAI_API_KEY=your_openai_api_key
TAVILY_API_KEY=your_tavily_api_key
```

### 3. 启动服务

```bash
# 使用启动脚本（推荐）
python scripts/start.py

# 或分别启动服务
# 启动API服务
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000

# 启动前端服务
python src/frontend/app.py
```

## 🐳 Docker部署

### 单容器部署

```bash
# 构建镜像
docker build -t intelligent-search-system .

# 运行容器
docker run -d \
  --name search-system \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_key \
  -e TAVILY_API_KEY=your_key \
  intelligent-search-system
```

### Docker Compose部署（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境部署

```bash
# 使用生产配置启动
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 包含缓存和代理
docker-compose --profile with-cache --profile with-proxy up -d
```

## ☁️ 云平台部署

### AWS ECS

1. 构建并推送镜像到ECR
2. 创建ECS任务定义
3. 配置负载均衡器
4. 设置环境变量和密钥

### Google Cloud Run

```bash
# 构建并部署
gcloud run deploy intelligent-search \
  --source . \
  --platform managed \
  --region us-central1 \
  --set-env-vars OPENAI_API_KEY=your_key,TAVILY_API_KEY=your_key
```

### Azure Container Instances

```bash
# 创建资源组
az group create --name search-system-rg --location eastus

# 部署容器
az container create \
  --resource-group search-system-rg \
  --name search-system \
  --image your-registry/intelligent-search-system \
  --ports 8000 \
  --environment-variables OPENAI_API_KEY=your_key TAVILY_API_KEY=your_key
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | OpenAI API密钥 | - | ✅ |
| `TAVILY_API_KEY` | Tavily搜索API密钥 | - | ✅ |
| `OPENAI_BASE_URL` | OpenAI API基础URL | https://api.openai.com/v1 | ❌ |
| `OPENAI_MODEL` | 使用的模型 | gpt-4 | ❌ |
| `DEBUG` | 调试模式 | false | ❌ |
| `LOG_LEVEL` | 日志级别 | INFO | ❌ |
| `API_HOST` | API服务主机 | 0.0.0.0 | ❌ |
| `API_PORT` | API服务端口 | 8000 | ❌ |
| `FRONTEND_HOST` | 前端服务主机 | 0.0.0.0 | ❌ |
| `FRONTEND_PORT` | 前端服务端口 | 7860 | ❌ |

### 性能调优

```bash
# 生产环境推荐配置
export WORKERS=4
export MAX_REQUESTS=1000
export TIMEOUT=60
export KEEPALIVE=2

# 启动多进程服务
gunicorn src.api.main:app \
  --workers $WORKERS \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --max-requests $MAX_REQUESTS \
  --timeout $TIMEOUT \
  --keep-alive $KEEPALIVE
```

## 📊 监控和日志

### 健康检查

```bash
# 基础健康检查
curl http://localhost:8000/api/v1/health

# 详细健康检查
curl -X POST http://localhost:8000/api/v1/health/detailed \
  -H "Content-Type: application/json" \
  -d '{"check_dependencies": true}'
```

### 日志管理

```bash
# 查看API日志
tail -f logs/api.log

# 查看用户日志
tail -f logs/user_friendly.log

# 查看开发者日志
tail -f logs/developer_detailed.log
```

### Prometheus监控

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'intelligent-search'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

## 🔒 安全配置

### HTTPS配置

```nginx
# nginx.conf
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### API密钥管理

```bash
# 使用密钥管理服务
export OPENAI_API_KEY=$(aws secretsmanager get-secret-value \
  --secret-id openai-api-key \
  --query SecretString --output text)
```

## 🚨 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   # 检查环境变量
   echo $OPENAI_API_KEY
   echo $TAVILY_API_KEY
   ```

2. **端口冲突**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8000
   lsof -i :8000
   ```

3. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install --force-reinstall -r requirements.txt
   ```

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la logs/
   chmod 755 logs/
   ```

### 性能问题

1. **响应慢**
   - 检查API密钥配额
   - 增加超时时间
   - 使用缓存

2. **内存占用高**
   - 调整worker数量
   - 启用日志轮转
   - 监控内存使用

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/*.log

# 分析响应时间
grep "execution_time" logs/api.log | awk '{print $NF}' | sort -n
```

## 📈 扩展部署

### 负载均衡

```yaml
# docker-compose.yml
version: '3.8'
services:
  search-api-1:
    build: .
    environment:
      - INSTANCE_ID=1
  
  search-api-2:
    build: .
    environment:
      - INSTANCE_ID=2
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
```

### 数据库集成

```python
# 添加数据库支持
DATABASE_URL=postgresql://user:pass@localhost/searchdb
REDIS_URL=redis://localhost:6379/0
```

### 微服务架构

```bash
# 拆分为独立服务
docker-compose -f docker-compose.microservices.yml up -d
```
