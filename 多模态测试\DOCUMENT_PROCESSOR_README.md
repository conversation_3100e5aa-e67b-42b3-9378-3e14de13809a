# 文档处理器 - Marker + 多模态模型增强

这个工具使用Marker提取PDF/DOCX文档内容，并通过OpenAI Vision API为图片添加智能描述，生成可直接用于Chat模型分析的增强Markdown文档。

## ✨ 核心特性

- 📄 **支持多格式**: PDF和DOCX文档
- 🖼️ **智能图片分析**: 使用GPT-4V等多模态模型分析图片内容
- 📝 **保持原始结构**: 在Markdown图片引用下方添加描述
- 🤖 **Chat模型就绪**: 生成的内容可直接用于AI分析
- ⚡ **批量处理**: 支持批量处理多个文档
- 🎯 **灵活配置**: 可自定义图片分析提示词

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置API密钥

```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### 3. 基础使用

```python
from document_processor import DocumentProcessor

# 初始化处理器
processor = DocumentProcessor()

# 处理文档
result = processor.process_document(
    file_path="your_document.pdf",
    output_dir="./output",
    analyze_images=True
)

print(f"增强Markdown已保存到: {result['enhanced_markdown']}")
```

## 📖 增强Markdown示例

**原始Marker输出：**
```markdown
# 文档标题
这是一段文字。
![image](image_1.png)
这是另一段文字。
```

**增强后的输出：**
```markdown
# 文档标题
这是一段文字。
![image](image_1.png)

**图片描述：** 这是一张显示销售数据的柱状图，横轴表示月份（1-12月），纵轴表示销售额（万元）。图表显示第三季度销售额最高，达到150万元。图表标题为"2024年月度销售统计"。

这是另一段文字。
```

## 🎯 Chat模型集成

```python
# 读取增强后的内容
with open(result['enhanced_markdown'], 'r', encoding='utf-8') as f:
    enhanced_content = f.read()

# 直接发送给任何Chat模型
chat_prompt = f"""
以下是一份处理后的文档内容，请帮我分析其中的关键信息：

{enhanced_content}

请总结文档的主要内容、关键数据和重要结论。
"""
```

## 🔧 高级配置

### 自定义图片分析提示词

```python
# 针对技术文档
tech_prompt = """
请分析这张图片，重点关注：
1. 技术架构图或流程图的结构
2. 代码片段或配置信息  
3. 数据表格或统计图表
4. 关键的技术概念或术语
用中文详细描述，适合技术人员理解。
"""

result = processor.process_document(
    file_path="technical_doc.pdf",
    analyze_images=True,
    image_analysis_prompt=tech_prompt
)
```

## ⚠️ 重要提醒

### API成本控制
- GPT-4V分析图片有一定成本，建议先测试小文档
- 可以设置`analyze_images=False`仅提取图片
- 考虑使用更便宜的模型如`gpt-4o-mini`

### 企业部署考虑
- 确保API密钥安全存储
- 考虑本地部署的多模态模型（如LLaVA）
- 注意数据隐私和合规要求

运行 `python example_usage.py` 查看更多使用示例。
