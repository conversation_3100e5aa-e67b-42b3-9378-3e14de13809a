#!/usr/bin/env python3
"""
Ollama视觉分析器使用示例
展示各种使用场景和最佳实践
"""

from ollama_vision_analyzer import OllamaVisionAnalyzer
import os

def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 初始化分析器
    analyzer = OllamaVisionAnalyzer()
    
    # 分析单张图片
    image_path = "test_image.jpg"  # 替换为你的图片路径
    question = "请详细描述这张图片的内容"
    
    if os.path.exists(image_path):
        result = analyzer.analyze_image(image_path, question)
        
        if result['success']:
            print(f"✅ 分析成功:")
            print(f"📸 图片: {image_path}")
            print(f"❓ 问题: {question}")
            print(f"🤖 回答: {result['response']}")
        else:
            print(f"❌ 分析失败: {result['error']}")
    else:
        print(f"❌ 图片文件不存在: {image_path}")

def example_different_questions():
    """不同类型问题示例"""
    print("\n=== 不同问题类型示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    image_path = "sample.jpg"  # 替换为你的图片路径
    
    # 不同类型的问题
    questions = [
        "这张图片中有什么物体?",
        "图片中的文字内容是什么?",
        "描述图片的颜色和构图",
        "这张图片表达了什么情感?",
        "如果这是一张图表，请分析其数据趋势",
        "这张图片适合用在什么场景?"
    ]
    
    if os.path.exists(image_path):
        for i, question in enumerate(questions, 1):
            print(f"\n📝 问题 {i}: {question}")
            result = analyzer.analyze_image(image_path, question)
            
            if result['success']:
                print(f"🤖 回答: {result['response'][:200]}...")  # 只显示前200字符
            else:
                print(f"❌ 失败: {result['error']}")
    else:
        print(f"❌ 请准备一张测试图片: {image_path}")

def example_stream_mode():
    """流式输出示例"""
    print("\n=== 流式输出示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    image_path = "test_image.jpg"
    question = "请详细分析这张图片，包括内容、构图、色彩等各个方面"
    
    if os.path.exists(image_path):
        print("🔄 使用流式输出模式...")
        result = analyzer.analyze_image(image_path, question, stream=True)
        
        if not result['success']:
            print(f"❌ 流式输出失败: {result['error']}")
    else:
        print(f"❌ 图片文件不存在: {image_path}")

def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    
    # 准备多张图片
    image_paths = [
        "J:\LLM-TT\多模态测试\屏幕截图 2025-07-29 164644.jpg",
    
    ]
    
    # 为每张图片准备不同的问题
    questions = [
        "描述这张图片的主要内容",
        "这张图片中有什么文字?",
        "分析这张图片的视觉元素"
    ]
    
    # 检查图片是否存在
    existing_images = [img for img in image_paths if os.path.exists(img)]
    
    if existing_images:
        print(f"📸 找到 {len(existing_images)} 张图片，开始批量处理...")
        
        results = analyzer.batch_analyze(
            existing_images, 
            questions[:len(existing_images)],
            output_file="batch_results.json"
        )
        
        # 显示结果摘要
        successful = sum(1 for r in results if r['success'])
        print(f"✅ 成功处理: {successful}/{len(results)} 张图片")
        
    else:
        print("❌ 没有找到可用的图片文件")
        print("💡 请准备一些测试图片: image1.jpg, image2.png, image3.jpg")

def example_ocr_analysis():
    """OCR文字识别示例"""
    print("\n=== OCR文字识别示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    
    # OCR相关的问题
    ocr_questions = [
        "请提取图片中的所有文字内容",
        "图片中的文字是什么语言?",
        "按从上到下、从左到右的顺序读出图片中的文字",
        "图片中有哪些数字和符号?",
        "如果这是一个表格，请提取表格内容"
    ]
    
    image_path = "text_image.jpg"  # 包含文字的图片
    
    if os.path.exists(image_path):
        for question in ocr_questions:
            print(f"\n📝 OCR问题: {question}")
            result = analyzer.analyze_image(image_path, question)
            
            if result['success']:
                print(f"🤖 识别结果: {result['response']}")
            else:
                print(f"❌ 识别失败: {result['error']}")
    else:
        print(f"❌ 请准备一张包含文字的图片: {image_path}")

def example_chart_analysis():
    """图表分析示例"""
    print("\n=== 图表分析示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    
    # 图表分析问题
    chart_questions = [
        "这是什么类型的图表?",
        "图表显示了什么数据?",
        "图表的横轴和纵轴分别表示什么?",
        "从图表中可以得出什么结论?",
        "图表中的最高值和最低值是多少?",
        "图表显示了什么趋势?"
    ]
    
    chart_image = "chart.png"  # 图表图片
    
    if os.path.exists(chart_image):
        for question in chart_questions:
            print(f"\n📊 图表问题: {question}")
            result = analyzer.analyze_image(chart_image, question)
            
            if result['success']:
                print(f"📈 分析结果: {result['response']}")
            else:
                print(f"❌ 分析失败: {result['error']}")
    else:
        print(f"❌ 请准备一张图表图片: {chart_image}")

def example_performance_test():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    image_path = "test_image.jpg"
    
    if os.path.exists(image_path):
        print("⏱️ 测试模型响应性能...")
        
        # 测试不同长度的问题
        test_questions = [
            "描述图片",  # 短问题
            "请详细描述这张图片的内容，包括主要物体、颜色、构图等",  # 中等问题
            "请从以下几个方面详细分析这张图片：1)主要内容和物体 2)色彩搭配和视觉效果 3)构图和布局 4)可能的拍摄场景和用途 5)给人的整体感受"  # 长问题
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n🧪 测试 {i}: {question[:30]}...")
            result = analyzer.analyze_image(image_path, question)
            
            if result['success']:
                # 计算性能指标
                if 'total_duration' in result:
                    duration_ms = result['total_duration'] / 1000000
                    print(f"⏱️ 总耗时: {duration_ms:.2f}ms")
                    
                    if 'eval_count' in result and result['eval_count'] > 0:
                        tokens_per_sec = result['eval_count'] / (duration_ms / 1000)
                        print(f"🚀 生成速度: {tokens_per_sec:.2f} tokens/s")
                        print(f"📝 生成token数: {result['eval_count']}")
                
                print(f"📏 回答长度: {len(result['response'])} 字符")
            else:
                print(f"❌ 测试失败: {result['error']}")
    else:
        print(f"❌ 图片文件不存在: {image_path}")

def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    analyzer = OllamaVisionAnalyzer()
    
    # 测试各种错误情况
    error_cases = [
        ("nonexistent.jpg", "测试不存在的文件"),
        ("test.txt", "测试不支持的文件格式"),  # 如果存在的话
    ]
    
    for image_path, description in error_cases:
        print(f"\n🧪 {description}: {image_path}")
        result = analyzer.analyze_image(image_path, "描述图片")
        
        if not result['success']:
            print(f"✅ 正确捕获错误: {result['error']}")
        else:
            print(f"⚠️ 意外成功: {result['response'][:100]}...")

def main():
    """运行所有示例"""
    print("🎯 Ollama视觉分析器示例演示")
    print("=" * 60)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_different_questions()
        example_stream_mode()
        example_batch_processing()
        example_ocr_analysis()
        example_chart_analysis()
        example_performance_test()
        example_error_handling()
        
        print("\n🎉 所有示例演示完成!")
        print("\n💡 使用提示:")
        print("1. 准备一些测试图片来体验完整功能")
        print("2. 运行 python ollama_vision_analyzer.py --interactive 进入交互模式")
        print("3. 使用 python ollama_vision_analyzer.py --help 查看所有参数")
        
    except KeyboardInterrupt:
        print("\n👋 演示被中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")

if __name__ == "__main__":
    main()
