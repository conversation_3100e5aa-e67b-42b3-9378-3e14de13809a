"""
规划引擎模块

实现ReAct模式的思考和规划功能。
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from ..utils.logging import DualLogger
from ..utils.exceptions import PlanningError


@dataclass
class PlanningResult:
    """规划结果数据类"""
    user_intent: str
    current_situation: str
    plan: List[str]
    next_action: str
    planning_time: str
    mode: str = "ReAct (Reasoning and Acting)"


class PlanningEngine:
    """规划引擎类"""
    
    def __init__(self, logger: Optional[DualLogger] = None):
        self.logger = logger or DualLogger()
    
    async def think_and_plan(
        self, 
        user_intent: str, 
        current_situation: str, 
        plan: List[str], 
        next_action: str
    ) -> PlanningResult:
        """
        ReAct模式的思考和规划
        
        Args:
            user_intent: 对用户核心需求的理解
            current_situation: 当前状态分析，包括已有信息和缺失信息
            plan: 详细的执行步骤列表，每步都要具体可执行
            next_action: 基于规划确定的下一个具体行动
            
        Returns:
            PlanningResult: 规划结果对象
            
        Raises:
            PlanningError: 规划执行失败
        """
        # 开始日志记录
        step_id = self.logger.start_step("思考与规划", {
            "user_intent": user_intent,
            "current_situation": current_situation,
            "plan": plan,
            "next_action": next_action
        })
        
        try:
            # 创建规划结果
            planning_result = PlanningResult(
                user_intent=user_intent,
                current_situation=current_situation,
                plan=plan,
                next_action=next_action,
                planning_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            # 格式化输出规划结果
            self._display_planning_result(planning_result)
            
            # 完成日志记录
            self.logger.complete_step(step_id, {
                "planning_result": {
                    "user_intent": user_intent,
                    "current_situation": current_situation,
                    "plan": plan,
                    "next_action": next_action,
                    "planning_time": planning_result.planning_time
                }
            })
            
            # 显示进度概要
            print(f"\n{self._get_progress_summary()}")
            
            return planning_result
            
        except Exception as e:
            self.logger.complete_step(step_id, error=str(e))
            raise PlanningError(
                f"规划执行失败: {str(e)}",
                error_code="PLANNING_ERROR",
                details={"exception": str(e)}
            )
    
    def think_and_plan_to_json(
        self, 
        user_intent: str, 
        current_situation: str, 
        plan: List[str], 
        next_action: str
    ) -> str:
        """
        ReAct模式的思考和规划（返回JSON字符串）
        
        Args:
            user_intent: 对用户核心需求的理解
            current_situation: 当前状态分析
            plan: 详细的执行步骤列表
            next_action: 下一个具体行动
            
        Returns:
            str: 规划结果的JSON字符串
        """
        step_id = self.logger.start_step("思考与规划", {
            "user_intent": user_intent,
            "current_situation": current_situation,
            "plan": plan,
            "next_action": next_action
        })
        
        try:
            planning_result = {
                "reasoning": {
                    "用户意图": user_intent,
                    "当前状况": current_situation,
                    "执行计划": plan
                },
                "acting": {
                    "下一步行动": next_action
                },
                "metadata": {
                    "规划时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "模式": "ReAct (Reasoning and Acting)"
                }
            }
            
            # 格式化输出规划结果
            result_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🧠 智能体思考与规划 (ReAct模式)                    ║
╠══════════════════════════════════════════════════════════════╣
║ 📋 用户意图: {user_intent:<50} ║
║ 📊 当前状况: {current_situation:<50} ║
║ 🎯 执行计划: {' → '.join(plan):<50} ║
║ ⚡ 下一步行动: {next_action:<48} ║
║ ⏰ 规划时间: {planning_result['metadata']['规划时间']:<50} ║
╚══════════════════════════════════════════════════════════════╝
            """
            
            print(result_text)
            
            # 完成日志记录
            self.logger.complete_step(step_id, {"planning_result": planning_result})
            
            # 显示进度概要
            print(f"\n{self._get_progress_summary()}")
            
            return json.dumps(planning_result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            self.logger.complete_step(step_id, error=str(e))
            raise PlanningError(
                f"规划执行失败: {str(e)}",
                error_code="PLANNING_ERROR",
                details={"exception": str(e)}
            )
    
    def _display_planning_result(self, result: PlanningResult) -> None:
        """显示规划结果"""
        result_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🧠 智能体思考与规划 (ReAct模式)                    ║
╠══════════════════════════════════════════════════════════════╣
║ 📋 用户意图: {result.user_intent:<50} ║
║ 📊 当前状况: {result.current_situation:<50} ║
║ 🎯 执行计划: {' → '.join(result.plan):<50} ║
║ ⚡ 下一步行动: {result.next_action:<48} ║
║ ⏰ 规划时间: {result.planning_time:<50} ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(result_text)
    
    def _get_progress_summary(self) -> str:
        """获取用户友好的进度概要"""
        completed = len([s for s in self.logger.execution_steps if s.status == "COMPLETED"])
        total = len(self.logger.execution_steps)
        
        if total == 0:
            return "🚀 准备开始..."
        
        progress_bar = "█" * completed + "░" * (total - completed)
        return f"📊 进度 [{progress_bar}] {completed}/{total} 步完成"
