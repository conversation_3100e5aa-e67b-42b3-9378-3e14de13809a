#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索功能
"""

import os
import json
import requests
from datetime import datetime

def test_tavily_search():
    """测试Tavily搜索API"""
    print("🔍 测试Tavily搜索API")
    
    # API配置
    tavily_api_key = os.getenv("TAVILY_API_KEY", "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")
    api_url = "https://api.tavily.com/search"
    
    # 测试查询
    query = "Python FastAPI教程"
    
    payload = {
        "api_key": tavily_api_key,
        "query": query,
        "max_results": 3,
        "include_answer": True,
        "search_depth": "basic"
    }
    
    try:
        print(f"📡 发送请求: {query}")
        response = requests.post(api_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 搜索成功!")
            print(f"📝 AI答案: {data.get('answer', '无')[:100]}...")
            print(f"📊 结果数量: {len(data.get('results', []))}")
            
            for i, result in enumerate(data.get('results', [])[:2], 1):
                print(f"\n📄 结果 {i}:")
                print(f"   标题: {result.get('title', '无标题')}")
                print(f"   链接: {result.get('url', '无链接')}")
                print(f"   摘要: {result.get('content', '无摘要')[:100]}...")
                
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def test_gradio_import():
    """测试Gradio导入"""
    print("\n🧪 测试Gradio导入")
    try:
        import gradio as gr
        print("✅ Gradio导入成功")
        print(f"📦 版本: {gr.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Gradio导入失败: {e}")
        return False

def test_fastapi_import():
    """测试FastAPI导入"""
    print("\n🧪 测试FastAPI导入")
    try:
        from fastapi import FastAPI
        import uvicorn
        print("✅ FastAPI导入成功")
        return True
    except ImportError as e:
        print(f"❌ FastAPI导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔬 智能搜索应用测试")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 测试导入
    results.append(("FastAPI导入", test_fastapi_import()))
    results.append(("Gradio导入", test_gradio_import()))
    
    # 测试搜索
    results.append(("Tavily搜索", test_tavily_search()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以启动应用")
        print("\n📝 启动命令:")
        print("   后端: python test_backend.py")
        print("   前端: python frontend_gradio.py")
    else:
        print("⚠️  部分测试失败，请检查环境配置")

if __name__ == "__main__":
    main()
