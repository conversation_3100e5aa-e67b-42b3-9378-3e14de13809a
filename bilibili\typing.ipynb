{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 速通Typing类\n", "\n", "## 1. <PERSON><PERSON>类的作用\n", "\n", "Typing类是Python中的一个内置类，用于**类型提示和类型检查**。它可以帮助开发者更好地理解代码的类型关系，提高代码的可读性和可维护性。说人话就是，你写一个函数的时候肯定对函数的入参和返回值是清楚的，但当你几个月不看一个代码，你可能会忘记这个变量是什么类型，这时候Typing类就派上用场了。\n", "\n", "### 举例\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "3.0\n"]}], "source": ["def add(a, b):\n", "\treturn a + b\n", "\n", "print(add(1, 2))\n", "\n", "def add(a: int, b: int) -> int:\n", "\treturn a + b\n", "\n", "print(add(1.0, 2.0))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON>类包含的主要类型\n", "\n", "### 2.1 基本类型\n", "\n", "- Any: 任意类型\n", "- Tuple: 元组类型，等价于python原生类型tuple\n", "- List: 列表类型，等价于python原生类型list\n", "- Dict: 字典类型，用于键值对的映射，等价于python原生类型dict\n", "\n", "### 2.2 复杂类型\n", "\n", "- Callable: 表示一个参数是可调用对象，基本等价于函数\n", "- Optional: 等价于Union[T, None]，表示一个参数可以是T类型或者None类型\n", "- Literal: 表示从一组固定值中取值\n", "- Union: 表示一个参数可以取多种类型\n", "\n", "### 补充：python原生类型\n", "\n", "- list: 列表类型\n", "- dict: 字典类型\n", "- tuple: 元组类型\n", "- int: 整数类型\n", "- float: 浮点数类型\n", "- bool: 布尔类型\n", "- str: 字符串类型\n", "\n", "## 3. 类型注解的基本使用语法"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from typing import List, Literal, Dict, Optional, Any, Union\n", "\n", "class TestClass:\n", "    a = 1\n", "\n", "# python版本最好大于3.10以上\n", "def test_fun(a: List[int], \n", "             b: Dict[str, List[int]],\n", "             c: Any, \n", "             d: Literal[\"a\", \"b\", \"c\"] = \"a\",\n", "             e: Optional[float] = 123, # 可以取None\n", "             f: float = 123,\n", "             g: Optional[TestClass] = None,\n", "             h: list[int] = [1, 2, 3],\n", "             i: dict[str, str] = {\"a\": \"b\"},\n", "             j: Union[List[List[str]], Dict[str, Dict[str, str]]] = [[\"a\", \"b\"]]\n", "             ) -> str:\n", "    \n", "    \n", "\treturn \"hahaha\""]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}