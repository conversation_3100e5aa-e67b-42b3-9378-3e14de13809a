#!/usr/bin/env python3
"""
ReAct Agent 项目安装脚本
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def setup_environment():
    """设置环境"""
    print("🚀 ReAct Agent 项目安装")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 安装依赖
    dependencies = [
        "openai>=1.0.0",
        "python-dotenv>=1.0.0", 
        "requests>=2.25.0",
        "asyncio"
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"安装 {dep}"):
            return False
    
    # 创建.env文件
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("🔄 创建.env文件...")
        try:
            env_example.rename(env_file)
            print("✅ .env文件创建完成")
            print("💡 请编辑.env文件，填入真实的API密钥")
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
            return False
    
    print("\n🎉 安装完成！")
    print("=" * 50)
    print("📋 下一步操作:")
    print("1. 编辑.env文件，配置API密钥")
    print("2. 运行: python run_react_agent.py")
    print("3. 或直接运行: python function_calling_with_planning.py")
    
    return True

def main():
    """主函数"""
    if not setup_environment():
        print("\n❌ 安装失败")
        sys.exit(1)
    
    print("\n✅ 安装成功！")

if __name__ == "__main__":
    main()
