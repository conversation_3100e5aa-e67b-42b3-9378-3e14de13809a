from openai import AsyncOpenAI
import os
import asyncio
import random
from datetime import datetime
import json
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 初始化OpenAI API客户端
client = AsyncOpenAI(
    api_key="hk-72n0gg10000506100b90ecb51c341412d508ccd25ed0e3ba",
    base_url="https://api.openai-hk.com/v1"
)

# 思考和规划工具 - 实现ReAct模式
async def think_and_plan(user_intent: str, current_situation: str, plan: list, next_action: str) -> str:
    """ReAct模式的思考和规划工具 - 在执行业务操作前进行结构化思考

    这个工具实现了ReAct (Reasoning and Acting) 模式：
    - Reasoning: 分析用户意图、当前状况、制定计划
    - Acting: 确定下一步具体行动

    Args:
        user_intent (str): 对用户核心需求的理解
        current_situation (str): 当前状态分析，包括已有信息和缺失信息
        plan (list): 详细的执行步骤列表，每步都要具体可执行
        next_action (str): 基于规划确定的下一个具体行动

    Returns:
        str: 结构化的规划结果
    """
    planning_result = {
        "reasoning": {
            "用户意图": user_intent,
            "当前状况": current_situation,
            "执行计划": plan
        },
        "acting": {
            "下一步行动": next_action
        },
        "metadata": {
            "规划时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "模式": "ReAct (Reasoning and Acting)"
        }
    }

    # 格式化输出规划结果 - 更专业的展示
    result_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🧠 智能体思考与规划 (ReAct模式)                    ║
╠══════════════════════════════════════════════════════════════╣
║ 📋 用户意图: {user_intent:<50} ║
║ 📊 当前状况: {current_situation:<50} ║
║ 🎯 执行计划: {' → '.join(plan):<50} ║
║ ⚡ 下一步行动: {next_action:<48} ║
║ ⏰ 规划时间: {planning_result['metadata']['规划时间']:<50} ║
╚══════════════════════════════════════════════════════════════╝
    """

    print(result_text)
    return json.dumps(planning_result, ensure_ascii=False, indent=2)

# 模拟天气查询工具
async def get_current_weather(location: str) -> str:
    weather_conditions = ["晴天", "多云", "雨天"]
    random_weather = random.choice(weather_conditions)
    return f"{location}今天是{random_weather}。"

# Tavily搜索工具 - 使用HTTP API
async def tavily_search(query: str, max_results: int = 5, include_answer: bool = True) -> str:
    """
    使用Tavily搜索引擎搜索信息（HTTP API方式）

    Args:
        query (str): 搜索查询
        max_results (int): 返回结果数量，默认5个
        include_answer (bool): 是否包含AI生成的答案，默认True

    Returns:
        str: 搜索结果的JSON字符串
    """
    try:
        # 获取API密钥
        tavily_api_key = os.getenv("TAVILY_API_KEY", "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")
        if not tavily_api_key:
            return json.dumps({
                "error": "TAVILY_API_KEY环境变量未设置",
                "results": []
            }, ensure_ascii=False)

        # API配置
        api_url = "https://api.tavily.com/search"
        payload = {
            "api_key": tavily_api_key,
            "query": query,
            "max_results": max_results,
            "include_answer": include_answer,
            "search_depth": "advanced"
        }

        # 发送HTTP请求
        response = requests.post(api_url, json=payload, timeout=30)

        if response.status_code == 200:
            data = response.json()

            # 格式化结果
            formatted_results = {
                "query": data.get("query", query),
                "answer": data.get("answer", ""),
                "results": []
            }

            for result in data.get("results", []):
                formatted_results["results"].append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0)
                })

            return json.dumps(formatted_results, ensure_ascii=False)
        else:
            return json.dumps({
                "error": f"Tavily API调用失败: HTTP {response.status_code}",
                "details": response.text,
                "results": []
            }, ensure_ascii=False)

    except requests.exceptions.RequestException as e:
        return json.dumps({
            "error": f"网络请求失败: {str(e)}",
            "results": []
        }, ensure_ascii=False)
    except Exception as e:
        return json.dumps({
            "error": f"Tavily搜索失败: {str(e)}",
            "results": []
        }, ensure_ascii=False)

# 查询当前时间的工具
async def get_current_time() -> str:
    current_datetime = datetime.now()
    formatted_time = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
    return f"当前时间：{formatted_time}。"

# 定义工具的function schema，添加think_and_plan工具
tools = [{
    "type": "function",
    "function": {
        "name": "think_and_plan",
        "description": "在执行任何业务操作前必须调用的思考工具。像人类一样先思考再行动。",
        "parameters": {
            "type": "object",
            "properties": {
                "user_intent": {
                    "type": "string",
                    "description": "你对用户核心需求的理解"
                },
                "current_situation": {
                    "type": "string",
                    "description": "当前状态分析，包括已有信息和缺失信息"
                },
                "plan": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "详细的执行步骤列表，每步都要具体可执行"
                },
                "next_action": {
                    "type": "string",
                    "description": "基于规划确定的下一个具体行动"
                }
            },
            "required": ["user_intent", "current_situation", "plan", "next_action"]
        }
    }
}, {
    "type": "function",
    "function": {
        "name": "get_current_time",
        "description": "当你想知道现在的时间时非常有用。",
    }
}, {
    "type": "function",
    "function": {
        "name": "get_current_weather",
        "description": "当你想查询指定城市的天气时非常有用。",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "城市或县区，比如北京市、杭州市、余杭区等。",
                }
            },
            "required": ["location"]
        }
    }
}, {
    "type": "function",
    "function": {
        "name": "tavily_search",
        "description": "使用Tavily搜索引擎搜索最新信息，支持实时网络搜索和AI生成答案摘要",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索查询关键词"
                },
                "max_results": {
                    "type": "integer",
                    "description": "返回结果数量，默认5个",
                    "default": 5,
                    "minimum": 1,
                    "maximum": 20
                },
                "include_answer": {
                    "type": "boolean",
                    "description": "是否包含AI生成的答案摘要，默认True",
                    "default": True
                }
            },
            "required": ["query"]
        }
    }
}]

# 函数映射
tool_mapping = {
    "think_and_plan": think_and_plan,
    "get_current_time": get_current_time,
    "get_current_weather": get_current_weather,
    "tavily_search": tavily_search
}

# assistant messages模板
assistant_messages_template = {
    "content": "",
    "refusal": None,
    "role": "assistant",
    "audio": None,
    "function_call": None,
    "tool_calls": [{
        "id": "call_xxx",
        "function": {
            "arguments": "",
            "name": "",
        },
        "type": "function",
        "index": 0,
    }],
}

# 异步函数调用函数
async def function_calling(query: str) -> tuple[str, str, str, list, str]:
    """函数调用函数，采用流式输出，兼容普通问答
    
    Args:
        query (str): 用户输入的query
    
    Returns:
        function_name (str): 工具名称
        function_arguments (str): 工具入参
        fun_id (str): 工具ID
        origin_messages (list): 原始消息
        response_content (str): 回答
    """
    origin_messages = [{
        "role": "system",
        "content": """# Agent核心工作流程

你是一个具备规划能力的AI助手。你的工作流程是：

1. **强制规划优先**：在调用任何业务工具前，必须先调用`think_and_plan`工具进行思考
2. **循环执行**：规划 → 执行 → 分析结果 → 重新规划，直到任务完成
3. **错误处理**：工具调用失败时，必须重新规划而不是盲目重试
4. **并行限制**：除非明确确认无依赖关系，否则禁止并行调用工具

记住：你不是一个急性子的实习生，而是一个深思熟虑的专业助手。"""
    }, {
        "role": "user",
        "content": query
    }]

    response = await client.chat.completions.create(
        model="gpt-4o",
        messages=origin_messages,
        tools=tools,
        tool_choice="auto",
        stream=True
    )
    
    function_name = ""
    function_arguments = ""
    response_content = ""
    fun_id = None
    first_chunk = True
    
    async for chunk in response:
        if chunk.choices[0].delta.tool_calls:
            if first_chunk:
                function_name = chunk.choices[0].delta.tool_calls[0].function.name
                function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
                fun_id = chunk.choices[0].delta.tool_calls[0].id
                first_chunk = False
            else:
                if chunk.choices[0].delta.tool_calls[0].function.arguments:
                    function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
        else:
            if chunk.choices[0].delta.content:
                response_content += chunk.choices[0].delta.content
                print(chunk.choices[0].delta.content, end="", flush=True)

    return function_name, function_arguments, fun_id, origin_messages, response_content

# 改进的ReAct工作流程管理器
class ReactWorkflowManager:
    def __init__(self, max_iterations=10):
        self.max_iterations = max_iterations
        self.current_iteration = 0
        self.conversation_history = []

    async def execute_tool_call(self, function_name: str, function_arguments: str, fun_id: str) -> str:
        """安全执行工具调用"""
        try:
            if function_name not in tool_mapping:
                return f"错误：未知工具 {function_name}"

            function = tool_mapping[function_name]
            function_arguments_dict = json.loads(function_arguments)
            result = await function(**function_arguments_dict)

            print(f"✅ 工具执行成功：{function_name}")
            return str(result)

        except Exception as e:
            error_msg = f"❌ 工具执行失败：{function_name}, 错误：{str(e)}"
            print(error_msg)
            return error_msg

    def update_conversation_history(self, function_name: str, function_arguments: str,
                                  fun_id: str, function_result: str, messages: list):
        """更新对话历史 - 修复tool_call_id匹配问题"""
        # 构建正确的assistant消息，包含工具调用
        assistant_message = {
            "role": "assistant",
            "content": None,
            "tool_calls": [{
                "id": fun_id,
                "type": "function",
                "function": {
                    "name": function_name,
                    "arguments": function_arguments
                }
            }]
        }

        # 构建工具响应消息
        tool_message = {
            "role": "tool",
            "content": function_result,
            "tool_call_id": fun_id
        }

        messages.append(assistant_message)
        messages.append(tool_message)

        return messages

# 主函数 - 真正的ReAct循环工作流程
async def main():
    # 用户问题
    query = "黑神话悟空是什么时候发售的"

    print(f"🔍 用户问题: {query}")
    print("="*80)
    print("🚀 启动真正的ReAct循环工作流程...")
    print("="*80)

    workflow_manager = ReactWorkflowManager()
    messages = [{
        "role": "system",
        "content": """# Agent核心工作流程

你是一个具备规划能力的AI助手。你的工作流程是：

1. **强制规划优先**：在调用任何业务工具前，必须先调用`think_and_plan`工具进行思考
2. **循环执行**：规划 → 执行 → 分析结果 → 重新规划，直到任务完成
3. **错误处理**：工具调用失败时，必须重新规划而不是盲目重试
4. **并行限制**：除非明确确认无依赖关系，否则禁止并行调用工具

记住：你不是一个急性子的实习生，而是一个深思熟虑的专业助手。"""
    }, {
        "role": "user",
        "content": query
    }]

    # ReAct循环：持续执行直到任务完成或达到最大迭代次数
    while workflow_manager.current_iteration < workflow_manager.max_iterations:
        workflow_manager.current_iteration += 1
        print(f"\n🔄 ReAct循环 - 第 {workflow_manager.current_iteration} 轮")
        print("-" * 60)

        # 调用大模型获取下一步行动
        response = await client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            tools=tools,
            tool_choice="auto",
            stream=True
        )

        # 解析响应
        function_name = ""
        function_arguments = ""
        fun_id = None
        response_content = ""
        first_chunk = True

        async for chunk in response:
            if chunk.choices[0].delta.tool_calls:
                if first_chunk:
                    function_name = chunk.choices[0].delta.tool_calls[0].function.name
                    function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
                    fun_id = chunk.choices[0].delta.tool_calls[0].id
                    first_chunk = False
                else:
                    if chunk.choices[0].delta.tool_calls[0].function.arguments:
                        function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
            else:
                if chunk.choices[0].delta.content:
                    response_content += chunk.choices[0].delta.content
                    print(chunk.choices[0].delta.content, end="", flush=True)

        # 如果有工具调用，执行工具
        if function_name:
            print(f"\n🔧 执行工具：{function_name}")
            print(f"📝 参数：{function_arguments}")

            # 验证第一次调用是否为规划工具
            if workflow_manager.current_iteration == 1 and function_name != "think_and_plan":
                print("⚠️  警告：第一次调用应该是think_and_plan工具！")
                print("🔄 强制插入规划步骤...")
                # 这里可以强制插入规划步骤

            # 执行工具
            function_result = await workflow_manager.execute_tool_call(
                function_name, function_arguments, fun_id
            )

            # 更新对话历史
            messages = workflow_manager.update_conversation_history(
                function_name, function_arguments, fun_id, function_result, messages
            )

        else:
            # 没有工具调用，说明任务可能已完成
            print(f"\n✅ 任务完成！最终回答：")
            print(response_content)
            break

    if workflow_manager.current_iteration >= workflow_manager.max_iterations:
        print(f"\n⚠️  达到最大迭代次数 ({workflow_manager.max_iterations})，停止执行")

if __name__ == "__main__":
    asyncio.run(main())
