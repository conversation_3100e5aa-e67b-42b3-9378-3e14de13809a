#!/usr/bin/env python3
"""
多模态测试项目启动器
帮助用户选择和启动合适的多模态方案
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印项目横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🎯 多模态测试项目                          ║
║                                                              ║
║  两种强大的多模态解决方案，满足不同场景需求                    ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查基础依赖"""
    print("🔍 检查基础依赖...")
    
    try:
        import ollama
        ollama_available = True
        print("✅ Ollama Python库已安装")
    except ImportError:
        ollama_available = False
        print("❌ Ollama Python库未安装")
    
    try:
        import openai
        openai_available = True
        print("✅ OpenAI库已安装")
    except ImportError:
        openai_available = False
        print("❌ OpenAI库未安装")
    
    try:
        from marker import convert_single_pdf
        marker_available = True
        print("✅ Marker库已安装")
    except ImportError:
        marker_available = False
        print("❌ Marker库未安装")
    
    return {
        'ollama': ollama_available,
        'openai': openai_available,
        'marker': marker_available
    }

def check_ollama_service():
    """检查Ollama服务状态"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            return True, model_names
        else:
            return False, []
    except Exception:
        return False, []

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖选项:")
    print("1. 安装基础依赖（Ollama + 图片处理）")
    print("2. 安装完整依赖（包含Marker文档处理）")
    print("3. 跳过安装")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        print("🔄 安装基础依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "ollama", "pillow", "tqdm", "rich", "requests"])
    elif choice == "2":
        print("🔄 安装完整依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    elif choice == "3":
        print("⏭️ 跳过依赖安装")
    else:
        print("❌ 无效选择")

def show_solution_menu():
    """显示方案选择菜单"""
    print("\n" + "="*60)
    print("🎯 请选择多模态解决方案:")
    print("="*60)
    
    print("\n📱 方案一：Ollama本地多模态分析")
    print("   ✅ 完全本地化，保护数据隐私")
    print("   ✅ 无API调用成本")
    print("   ✅ 支持实时图片分析和问答")
    print("   ⚠️  需要8GB+GPU内存")
    print("   🎯 适合：单张图片分析、隐私要求高的场景")
    
    print("\n📄 方案二：Marker + OpenAI文档处理")
    print("   ✅ 支持PDF/DOCX文档转换")
    print("   ✅ 高质量图片分析（GPT-4V）")
    print("   ✅ 生成Chat模型友好的增强文档")
    print("   ⚠️  需要OpenAI API密钥和费用")
    print("   🎯 适合：文档处理、高质量分析需求")
    
    print("\n🔧 其他选项：")
    print("   3. 查看项目文档")
    print("   4. 运行示例代码")
    print("   5. 检查环境状态")
    print("   6. 安装/更新依赖")
    print("   0. 退出")
    
    return input("\n请选择 (0-6): ").strip()

def start_ollama_solution():
    """启动Ollama方案"""
    print("\n🚀 启动Ollama本地多模态分析...")
    
    # 检查Ollama服务
    service_running, models = check_ollama_service()
    
    if not service_running:
        print("❌ Ollama服务未运行")
        print("💡 请先启动Ollama服务: ollama serve")
        return
    
    print(f"✅ Ollama服务正在运行")
    print(f"📋 可用模型: {', '.join(models) if models else '无'}")
    
    # 检查qwen2.5-vl模型
    if not any('qwen2.5-vl' in model for model in models):
        print("⚠️  未找到qwen2.5-vl模型")
        install_model = input("是否现在下载qwen2.5-vl:7b模型? (y/N): ").strip().lower()
        if install_model in ['y', 'yes', '是']:
            print("📥 下载模型中...")
            subprocess.run(["ollama", "pull", "qwen2.5-vl:7b"])
        else:
            print("💡 你也可以手动下载: ollama pull qwen2.5-vl:7b")
            return
    
    # 启动交互模式
    print("\n🎯 启动交互式图片分析...")
    try:
        subprocess.run([sys.executable, "ollama_vision_analyzer.py", "--interactive"])
    except KeyboardInterrupt:
        print("\n👋 退出Ollama分析器")
    except FileNotFoundError:
        print("❌ 找不到ollama_vision_analyzer.py文件")

def start_document_solution():
    """启动文档处理方案"""
    print("\n🚀 启动Marker + OpenAI文档处理...")
    
    # 检查OpenAI API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 未设置OpenAI API密钥")
        api_key = input("请输入OpenAI API密钥 (或按Enter跳过): ").strip()
        if api_key:
            os.environ['OPENAI_API_KEY'] = api_key
        else:
            print("💡 你可以设置环境变量: export OPENAI_API_KEY='your-key'")
            return
    
    print("✅ OpenAI API密钥已配置")
    
    # 运行示例
    print("\n📖 运行文档处理示例...")
    try:
        subprocess.run([sys.executable, "example_usage.py"])
    except KeyboardInterrupt:
        print("\n👋 退出文档处理器")
    except FileNotFoundError:
        print("❌ 找不到example_usage.py文件")

def show_documentation():
    """显示文档"""
    print("\n📚 项目文档:")
    print("1. 项目总览 - README.md")
    print("2. Ollama方案详细文档 - OLLAMA_VISION_README.md")
    print("3. 文档处理方案详细文档 - DOCUMENT_PROCESSOR_README.md")
    
    choice = input("\n选择要查看的文档 (1-3): ").strip()
    
    docs = {
        '1': 'README.md',
        '2': 'OLLAMA_VISION_README.md', 
        '3': 'DOCUMENT_PROCESSOR_README.md'
    }
    
    if choice in docs:
        doc_file = docs[choice]
        if os.path.exists(doc_file):
            print(f"\n📖 打开文档: {doc_file}")
            # 在Windows上使用默认程序打开
            if sys.platform == "win32":
                os.startfile(doc_file)
            else:
                subprocess.run(["open" if sys.platform == "darwin" else "xdg-open", doc_file])
        else:
            print(f"❌ 文档文件不存在: {doc_file}")
    else:
        print("❌ 无效选择")

def run_examples():
    """运行示例代码"""
    print("\n🧪 可用示例:")
    print("1. Ollama图片分析示例")
    print("2. 文档处理示例")
    
    choice = input("\n选择要运行的示例 (1-2): ").strip()
    
    if choice == "1":
        if os.path.exists("ollama_vision_examples.py"):
            print("🔄 运行Ollama示例...")
            subprocess.run([sys.executable, "ollama_vision_examples.py"])
        else:
            print("❌ 示例文件不存在")
    elif choice == "2":
        if os.path.exists("example_usage.py"):
            print("🔄 运行文档处理示例...")
            subprocess.run([sys.executable, "example_usage.py"])
        else:
            print("❌ 示例文件不存在")
    else:
        print("❌ 无效选择")

def check_environment():
    """检查环境状态"""
    print("\n🔍 环境状态检查:")
    print("="*40)
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查依赖
    deps = check_dependencies()
    
    # 检查Ollama服务
    service_running, models = check_ollama_service()
    print(f"\n🤖 Ollama服务: {'✅ 运行中' if service_running else '❌ 未运行'}")
    if service_running and models:
        print(f"📋 可用模型: {', '.join(models)}")
    
    # 检查OpenAI API
    api_key = os.getenv('OPENAI_API_KEY')
    print(f"🔑 OpenAI API: {'✅ 已配置' if api_key else '❌ 未配置'}")
    
    # 检查文件
    required_files = [
        'ollama_vision_analyzer.py',
        'document_processor.py',
        'requirements.txt'
    ]
    
    print(f"\n📁 项目文件:")
    for file in required_files:
        exists = os.path.exists(file)
        print(f"   {file}: {'✅' if exists else '❌'}")

def main():
    """主函数"""
    print_banner()
    
    # 检查当前目录
    current_dir = Path.cwd().name
    if current_dir != "多模态测试":
        print("⚠️  建议在'多模态测试'目录下运行此脚本")
        print(f"当前目录: {Path.cwd()}")
    
    while True:
        try:
            choice = show_solution_menu()
            
            if choice == "1":
                start_ollama_solution()
            elif choice == "2":
                start_document_solution()
            elif choice == "3":
                show_documentation()
            elif choice == "4":
                run_examples()
            elif choice == "5":
                check_environment()
            elif choice == "6":
                install_dependencies()
            elif choice == "0":
                print("\n👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
