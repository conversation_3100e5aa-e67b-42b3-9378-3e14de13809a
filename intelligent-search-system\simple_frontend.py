#!/usr/bin/env python3
"""
简化版前端测试
"""

import sys
import os
import gradio as gr
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_connection():
    """测试API连接"""
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/health", timeout=5)
        if response.status_code == 200:
            return "✅ API连接正常", response.json()
        else:
            return f"❌ API响应错误: {response.status_code}", None
    except Exception as e:
        return f"❌ API连接失败: {e}", None

def simple_search(query):
    """简单搜索功能"""
    if not query.strip():
        return "请输入搜索关键词"
    
    try:
        # 测试API连接
        status, data = test_api_connection()
        if "❌" in status:
            return f"API服务不可用: {status}"
        
        # 调用搜索API
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/search",
            json={"query": query, "max_results": 3},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return f"搜索成功！\n\n查询: {query}\n\n结果: {result}"
        else:
            return f"搜索失败: HTTP {response.status_code}\n{response.text}"
            
    except Exception as e:
        return f"搜索出错: {e}"

def create_simple_app():
    """创建简化的Gradio应用"""
    
    with gr.Blocks(title="智能搜索系统 - 测试版") as app:
        gr.Markdown("# 🔍 智能搜索系统")
        gr.Markdown("简化版前端，用于测试API连接和基本搜索功能")
        
        with gr.Row():
            with gr.Column():
                query_input = gr.Textbox(
                    label="搜索查询",
                    placeholder="输入您想搜索的内容...",
                    lines=2
                )
                search_btn = gr.Button("🔍 搜索", variant="primary")
                
            with gr.Column():
                result_output = gr.Textbox(
                    label="搜索结果",
                    lines=10,
                    max_lines=20
                )
        
        # API连接测试
        with gr.Row():
            test_btn = gr.Button("🔧 测试API连接")
            api_status = gr.Textbox(label="API状态", lines=2)
        
        # 绑定事件
        search_btn.click(
            fn=simple_search,
            inputs=[query_input],
            outputs=[result_output]
        )
        
        test_btn.click(
            fn=lambda: test_api_connection()[0],
            outputs=[api_status]
        )
    
    return app

def main():
    """主函数"""
    print("🎨 启动简化版前端...")
    
    try:
        app = create_simple_app()
        
        print("   地址: http://127.0.0.1:7863")
        print("   正在启动Gradio服务...")
        
        # 使用最简单的启动方式
        app.launch(
            server_name="127.0.0.1",
            server_port=7863,
            share=False,
            inbrowser=True,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
