"""
双层日志系统

提供用户友好的界面反馈和开发者详细的调试日志。
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from .config import get_settings


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    STEP = "STEP"


@dataclass
class ExecutionStep:
    """执行步骤的详细记录"""
    step_id: str
    step_name: str
    status: str  # PLANNED, EXECUTING, COMPLETED, FAILED
    start_time: str
    end_time: str = ""
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    error_info: str = ""
    duration_ms: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class DualLogger:
    """双层日志系统 - 用户友好 + 开发者详细"""

    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or datetime.now().strftime("%Y%m%d_%H%M%S")
        settings = get_settings()
        self.log_dir = Path(settings.log_dir)
        self.log_dir.mkdir(exist_ok=True)

        # 开发者日志文件
        self.dev_log_file = self.log_dir / f"dev_log_{self.session_id}.md"
        self.step_log_file = self.log_dir / f"steps_{self.session_id}.json"

        # 执行步骤跟踪
        self.execution_steps: List[ExecutionStep] = []
        self.current_step_id = 0

        # 初始化日志文件
        self._init_dev_log()

    def _init_dev_log(self) -> None:
        """初始化开发者日志文件"""
        header = f"""# ReAct Agent 执行日志
**会话ID**: {self.session_id}
**开始时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**日志级别**: DEBUG, INFO, WARNING, ERROR, STEP

---

## 📋 执行概览
- ✅ 已完成步骤: 0
- 🔄 当前步骤: 初始化
- ❌ 失败步骤: 0
- ⏱️ 总耗时: 0ms

---

## 📝 详细日志

"""
        with open(self.dev_log_file, 'w', encoding='utf-8') as f:
            f.write(header)

    def log(self, level: LogLevel, message: str, data: Optional[Dict] = None) -> None:
        """写入开发者日志"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

        log_entry = f"### [{timestamp}] {level.value}\n{message}\n"

        if data:
            log_entry += f"```json\n{json.dumps(data, ensure_ascii=False, indent=2)}\n```\n"

        log_entry += "\n---\n\n"

        with open(self.dev_log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

    def start_step(self, step_name: str, input_data: Optional[Dict] = None) -> str:
        """开始一个执行步骤"""
        self.current_step_id += 1
        step_id = f"step_{self.current_step_id:03d}"

        step = ExecutionStep(
            step_id=step_id,
            step_name=step_name,
            status="EXECUTING",
            start_time=datetime.now().isoformat(),
            input_data=input_data or {}
        )

        self.execution_steps.append(step)

        # 记录到开发者日志
        self.log(LogLevel.STEP, f"🚀 开始执行步骤: {step_name}", {
            "step_id": step_id,
            "input_data": input_data
        })

        # 用户友好的反馈
        print(f"🔄 正在执行: {step_name}")

        return step_id

    def complete_step(
        self, 
        step_id: str, 
        output_data: Optional[Dict] = None, 
        error: Optional[str] = None
    ) -> None:
        """完成一个执行步骤"""
        step = next((s for s in self.execution_steps if s.step_id == step_id), None)
        if not step:
            return

        step.end_time = datetime.now().isoformat()
        step.output_data = output_data or {}

        if error:
            step.status = "FAILED"
            step.error_info = error
            self.log(LogLevel.ERROR, f"❌ 步骤失败: {step.step_name}", {
                "step_id": step_id,
                "error": error,
                "output_data": output_data
            })
            print(f"❌ 执行失败: {step.step_name} - {error}")
        else:
            step.status = "COMPLETED"
            self.log(LogLevel.STEP, f"✅ 步骤完成: {step.step_name}", {
                "step_id": step_id,
                "output_data": output_data
            })
            print(f"✅ 已完成: {step.step_name}")

        # 计算耗时
        if step.start_time and step.end_time:
            start = datetime.fromisoformat(step.start_time)
            end = datetime.fromisoformat(step.end_time)
            step.duration_ms = int((end - start).total_seconds() * 1000)

        # 保存步骤详情到JSON
        self._save_steps_json()

        # 更新概览
        self._update_overview()

    def _save_steps_json(self) -> None:
        """保存步骤详情到JSON文件"""
        steps_data = {
            "session_id": self.session_id,
            "total_steps": len(self.execution_steps),
            "completed_steps": len([s for s in self.execution_steps if s.status == "COMPLETED"]),
            "failed_steps": len([s for s in self.execution_steps if s.status == "FAILED"]),
            "steps": [step.to_dict() for step in self.execution_steps]
        }

        with open(self.step_log_file, 'w', encoding='utf-8') as f:
            json.dump(steps_data, f, ensure_ascii=False, indent=2)

    def _update_overview(self) -> None:
        """更新日志文件的概览部分"""
        completed = len([s for s in self.execution_steps if s.status == "COMPLETED"])
        failed = len([s for s in self.execution_steps if s.status == "FAILED"])
        total_duration = sum(s.duration_ms for s in self.execution_steps if s.duration_ms > 0)

        # 读取现有内容
        with open(self.dev_log_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新概览部分
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '- ✅ 已完成步骤:' in line:
                lines[i] = f"- ✅ 已完成步骤: {completed}"
            elif '- 🔄 当前步骤:' in line:
                current_step = "完成" if completed == len(self.execution_steps) else f"步骤{len(self.execution_steps)}"
                lines[i] = f"- 🔄 当前步骤: {current_step}"
            elif '- ❌ 失败步骤:' in line:
                lines[i] = f"- ❌ 失败步骤: {failed}"
            elif '- ⏱️ 总耗时:' in line:
                lines[i] = f"- ⏱️ 总耗时: {total_duration}ms"

        # 写回文件
        with open(self.dev_log_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

    def get_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        completed = len([s for s in self.execution_steps if s.status == "COMPLETED"])
        failed = len([s for s in self.execution_steps if s.status == "FAILED"])
        total_duration = sum(s.duration_ms for s in self.execution_steps if s.duration_ms > 0)
        
        return {
            "session_id": self.session_id,
            "total_steps": len(self.execution_steps),
            "completed_steps": completed,
            "failed_steps": failed,
            "total_duration_ms": total_duration,
            "success_rate": completed / len(self.execution_steps) if self.execution_steps else 0
        }
