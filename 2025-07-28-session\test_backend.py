#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版后端测试
"""

import os
import json
import requests
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 简化的日志类
class SimpleLogger:
    def __init__(self):
        self.session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.steps = []
        self.current_step = 0
    
    def start_step(self, name: str, data: Dict = None):
        self.current_step += 1
        step_id = f"step_{self.current_step:03d}"
        step = {
            "step_id": step_id,
            "name": name,
            "start_time": datetime.now().isoformat(),
            "input_data": data or {}
        }
        self.steps.append(step)
        print(f"🔄 开始: {name}")
        return step_id
    
    def complete_step(self, step_id: str, output_data: Dict = None, error: str = None):
        for step in self.steps:
            if step["step_id"] == step_id:
                step["end_time"] = datetime.now().isoformat()
                step["output_data"] = output_data or {}
                step["error"] = error or ""
                step["status"] = "ERROR" if error else "COMPLETED"
                status = "❌" if error else "✅"
                print(f"{status} 完成: {step['name']}")
                break

# 初始化
app = FastAPI(title="简化搜索API", version="1.0.0")
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"])
logger = SimpleLogger()

# 请求响应模型
class SearchRequest(BaseModel):
    query: str
    max_results: Optional[int] = 5

class SearchResponse(BaseModel):
    query: str
    answer: str
    results: List[Dict]
    execution_time: float
    session_id: str

# 搜索功能
async def simple_search(query: str, max_results: int = 5) -> Dict:
    """简化的搜索功能"""
    step_id = logger.start_step(f"搜索: {query}", {"query": query, "max_results": max_results})
    
    try:
        # 使用Tavily API
        tavily_api_key = os.getenv("TAVILY_API_KEY", "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")
        api_url = "https://api.tavily.com/search"
        
        payload = {
            "api_key": tavily_api_key,
            "query": query,
            "max_results": max_results,
            "include_answer": True,
            "search_depth": "basic"
        }
        
        response = requests.post(api_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            result = {
                "query": data.get("query", query),
                "answer": data.get("answer", ""),
                "results": data.get("results", [])
            }
            logger.complete_step(step_id, {"results_count": len(result["results"])})
            return result
        else:
            error_msg = f"API调用失败: {response.status_code}"
            logger.complete_step(step_id, error=error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
            
    except Exception as e:
        error_msg = f"搜索失败: {str(e)}"
        logger.complete_step(step_id, error=error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/")
async def root():
    return {"message": "简化搜索API", "version": "1.0.0"}

@app.post("/search", response_model=SearchResponse)
async def search(request: SearchRequest):
    """搜索接口"""
    start_time = datetime.now()
    
    try:
        result = await simple_search(request.query, request.max_results)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return SearchResponse(
            query=result["query"],
            answer=result["answer"],
            results=result["results"],
            execution_time=execution_time,
            session_id=logger.session_id
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/logs")
async def get_logs():
    """获取日志"""
    return {"session_id": logger.session_id, "steps": logger.steps}

@app.get("/health")
async def health():
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    print("🚀 启动简化搜索API")
    print("📡 地址: http://localhost:8000")
    print("📚 文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
