from openai import AsyncOpenAI
import os
import asyncio
import random
from datetime import datetime
import json
import requests
from dotenv import load_dotenv
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum

# 加载环境变量
load_dotenv()

# ==================== 双层日志系统 ====================

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    STEP = "STEP"

@dataclass
class ExecutionStep:
    """执行步骤的详细记录"""
    step_id: str
    step_name: str
    status: str  # PLANNED, EXECUTING, COMPLETED, FAILED
    start_time: str
    end_time: str = ""
    input_data: Dict[str, Any] = None
    output_data: Dict[str, Any] = None
    error_info: str = ""
    duration_ms: int = 0

    def to_dict(self):
        return asdict(self)

class DualLogger:
    """双层日志系统 - 用户友好 + 开发者详细"""

    def __init__(self, session_id: str = None):
        self.session_id = session_id or datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)

        # 开发者日志文件
        self.dev_log_file = self.log_dir / f"dev_log_{self.session_id}.md"
        self.step_log_file = self.log_dir / f"steps_{self.session_id}.json"

        # 执行步骤跟踪
        self.execution_steps: List[ExecutionStep] = []
        self.current_step_id = 0

        # 初始化日志文件
        self._init_dev_log()

    def _init_dev_log(self):
        """初始化开发者日志文件"""
        header = f"""# ReAct Agent 执行日志
**会话ID**: {self.session_id}
**开始时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**日志级别**: DEBUG, INFO, WARNING, ERROR, STEP

---

## 📋 执行概览
- ✅ 已完成步骤: 0
- 🔄 当前步骤: 初始化
- ❌ 失败步骤: 0
- ⏱️ 总耗时: 0ms

---

## 📝 详细日志

"""
        with open(self.dev_log_file, 'w', encoding='utf-8') as f:
            f.write(header)

    def log(self, level: LogLevel, message: str, data: Dict = None):
        """写入开发者日志"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

        log_entry = f"### [{timestamp}] {level.value}\n{message}\n"

        if data:
            log_entry += f"```json\n{json.dumps(data, ensure_ascii=False, indent=2)}\n```\n"

        log_entry += "\n---\n\n"

        with open(self.dev_log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

    def start_step(self, step_name: str, input_data: Dict = None) -> str:
        """开始一个执行步骤"""
        self.current_step_id += 1
        step_id = f"step_{self.current_step_id:03d}"

        step = ExecutionStep(
            step_id=step_id,
            step_name=step_name,
            status="EXECUTING",
            start_time=datetime.now().isoformat(),
            input_data=input_data or {}
        )

        self.execution_steps.append(step)

        # 记录到开发者日志
        self.log(LogLevel.STEP, f"🚀 开始执行步骤: {step_name}", {
            "step_id": step_id,
            "input_data": input_data
        })

        # 用户友好的反馈
        print(f"🔄 正在执行: {step_name}")

        return step_id

    def complete_step(self, step_id: str, output_data: Dict = None, error: str = None):
        """完成一个执行步骤"""
        step = next((s for s in self.execution_steps if s.step_id == step_id), None)
        if not step:
            return

        step.end_time = datetime.now().isoformat()
        step.output_data = output_data or {}

        if error:
            step.status = "FAILED"
            step.error_info = error
            self.log(LogLevel.ERROR, f"❌ 步骤失败: {step.step_name}", {
                "step_id": step_id,
                "error": error,
                "output_data": output_data
            })
            print(f"❌ 执行失败: {step.step_name} - {error}")
        else:
            step.status = "COMPLETED"
            self.log(LogLevel.STEP, f"✅ 步骤完成: {step.step_name}", {
                "step_id": step_id,
                "output_data": output_data
            })
            print(f"✅ 已完成: {step.step_name}")

        # 计算耗时
        if step.start_time and step.end_time:
            start = datetime.fromisoformat(step.start_time)
            end = datetime.fromisoformat(step.end_time)
            step.duration_ms = int((end - start).total_seconds() * 1000)

        # 保存步骤详情到JSON
        self._save_steps_json()

        # 更新概览
        self._update_overview()

    def _save_steps_json(self):
        """保存步骤详情到JSON文件"""
        steps_data = {
            "session_id": self.session_id,
            "total_steps": len(self.execution_steps),
            "completed_steps": len([s for s in self.execution_steps if s.status == "COMPLETED"]),
            "failed_steps": len([s for s in self.execution_steps if s.status == "FAILED"]),
            "steps": [step.to_dict() for step in self.execution_steps]
        }

        with open(self.step_log_file, 'w', encoding='utf-8') as f:
            json.dump(steps_data, f, ensure_ascii=False, indent=2)

    def _update_overview(self):
        """更新日志文件的概览部分"""
        completed = len([s for s in self.execution_steps if s.status == "COMPLETED"])
        failed = len([s for s in self.execution_steps if s.status == "FAILED"])
        total_duration = sum(s.duration_ms for s in self.execution_steps if s.duration_ms > 0)

        # 读取现有内容
        with open(self.dev_log_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新概览部分
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '- ✅ 已完成步骤:' in line:
                lines[i] = f"- ✅ 已完成步骤: {completed}"
            elif '- 🔄 当前步骤:' in line:
                current_step = "完成" if completed == len(self.execution_steps) else f"步骤{len(self.execution_steps)}"
                lines[i] = f"- 🔄 当前步骤: {current_step}"
            elif '- ❌ 失败步骤:' in line:
                lines[i] = f"- ❌ 失败步骤: {failed}"
            elif '- ⏱️ 总耗时:' in line:
                lines[i] = f"- ⏱️ 总耗时: {total_duration}ms"

        # 写回文件
        with open(self.dev_log_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

    def get_progress_summary(self) -> str:
        """获取用户友好的进度概要"""
        completed = len([s for s in self.execution_steps if s.status == "COMPLETED"])
        total = len(self.execution_steps)

        if total == 0:
            return "🚀 准备开始..."

        progress_bar = "█" * completed + "░" * (total - completed)
        return f"📊 进度 [{progress_bar}] {completed}/{total} 步完成"

# 全局日志器
logger = DualLogger()

# ==================== 原有代码继续 ====================

# 初始化OpenAI API客户端
client = AsyncOpenAI(
    api_key="hk-72n0gg10000506100b90ecb51c341412d508ccd25ed0e3ba",
    base_url="https://api.openai-hk.com/v1"
)

# 思考和规划工具 - 实现ReAct模式 (集成双层日志)
async def think_and_plan(user_intent: str, current_situation: str, plan: list, next_action: str) -> str:
    """ReAct模式的思考和规划工具 - 在执行业务操作前进行结构化思考

    这个工具实现了ReAct (Reasoning and Acting) 模式：
    - Reasoning: 分析用户意图、当前状况、制定计划
    - Acting: 确定下一步具体行动

    Args:
        user_intent (str): 对用户核心需求的理解
        current_situation (str): 当前状态分析，包括已有信息和缺失信息
        plan (list): 详细的执行步骤列表，每步都要具体可执行
        next_action (str): 基于规划确定的下一个具体行动

    Returns:
        str: 结构化的规划结果
    """
    # 开始日志记录
    step_id = logger.start_step("思考与规划", {
        "user_intent": user_intent,
        "current_situation": current_situation,
        "plan": plan,
        "next_action": next_action
    })

    try:
        planning_result = {
            "reasoning": {
                "用户意图": user_intent,
                "当前状况": current_situation,
                "执行计划": plan
            },
            "acting": {
                "下一步行动": next_action
            },
            "metadata": {
                "规划时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "模式": "ReAct (Reasoning and Acting)"
            }
        }

        # 格式化输出规划结果 - 更专业的展示
        result_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🧠 智能体思考与规划 (ReAct模式)                    ║
╠══════════════════════════════════════════════════════════════╣
║ 📋 用户意图: {user_intent:<50} ║
║ 📊 当前状况: {current_situation:<50} ║
║ 🎯 执行计划: {' → '.join(plan):<50} ║
║ ⚡ 下一步行动: {next_action:<48} ║
║ ⏰ 规划时间: {planning_result['metadata']['规划时间']:<50} ║
╚══════════════════════════════════════════════════════════════╝
        """

        print(result_text)

        # 完成日志记录
        logger.complete_step(step_id, {"planning_result": planning_result})

        # 显示进度概要
        print(f"\n{logger.get_progress_summary()}")

        return json.dumps(planning_result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.complete_step(step_id, error=str(e))
        raise

# 模拟天气查询工具 (集成双层日志)
async def get_current_weather(location: str) -> str:
    # 开始日志记录
    step_id = logger.start_step(f"查询天气: {location}", {"location": location})

    try:
        weather_conditions = ["晴天", "多云", "雨天"]
        random_weather = random.choice(weather_conditions)
        result = f"{location}今天是{random_weather}。"

        # 完成日志记录
        logger.complete_step(step_id, {
            "location": location,
            "weather": random_weather,
            "result": result
        })

        return result
    except Exception as e:
        logger.complete_step(step_id, error=str(e))
        raise

# Tavily搜索工具 - 使用HTTP API (集成双层日志)
async def tavily_search(query: str, max_results: int = 5, include_answer: bool = True) -> str:
    """
    使用Tavily搜索引擎搜索信息（HTTP API方式）

    Args:
        query (str): 搜索查询
        max_results (int): 返回结果数量，默认5个
        include_answer (bool): 是否包含AI生成的答案，默认True

    Returns:
        str: 搜索结果的JSON字符串
    """
    # 开始日志记录
    step_id = logger.start_step(f"Tavily搜索: {query}", {
        "query": query,
        "max_results": max_results,
        "include_answer": include_answer
    })

    try:
        # 获取API密钥
        tavily_api_key = os.getenv("TAVILY_API_KEY", "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")
        if not tavily_api_key:
            error_result = json.dumps({
                "error": "TAVILY_API_KEY环境变量未设置",
                "results": []
            }, ensure_ascii=False)
            logger.complete_step(step_id, error="API密钥未设置")
            return error_result

        # API配置
        api_url = "https://api.tavily.com/search"
        payload = {
            "api_key": tavily_api_key,
            "query": query,
            "max_results": max_results,
            "include_answer": include_answer,
            "search_depth": "advanced"
        }

        # 发送HTTP请求
        response = requests.post(api_url, json=payload, timeout=30)

        if response.status_code == 200:
            data = response.json()

            # 格式化结果
            formatted_results = {
                "query": data.get("query", query),
                "answer": data.get("answer", ""),
                "results": []
            }

            for result in data.get("results", []):
                formatted_results["results"].append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0)
                })

            # 完成日志记录
            logger.complete_step(step_id, {
                "results_count": len(formatted_results["results"]),
                "has_answer": bool(formatted_results["answer"]),
                "api_response_code": response.status_code
            })

            return json.dumps(formatted_results, ensure_ascii=False)
        else:
            error_result = json.dumps({
                "error": f"Tavily API调用失败: HTTP {response.status_code}",
                "details": response.text,
                "results": []
            }, ensure_ascii=False)
            logger.complete_step(step_id, error=f"HTTP {response.status_code}: {response.text}")
            return error_result

    except requests.exceptions.RequestException as e:
        error_result = json.dumps({
            "error": f"网络请求失败: {str(e)}",
            "results": []
        }, ensure_ascii=False)
        logger.complete_step(step_id, error=f"网络请求失败: {str(e)}")
        return error_result
    except Exception as e:
        error_result = json.dumps({
            "error": f"Tavily搜索失败: {str(e)}",
            "results": []
        }, ensure_ascii=False)
        logger.complete_step(step_id, error=f"搜索失败: {str(e)}")
        return error_result

# 查询当前时间的工具 (集成双层日志)
async def get_current_time() -> str:
    # 开始日志记录
    step_id = logger.start_step("查询当前时间", {})

    try:
        current_datetime = datetime.now()
        formatted_time = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
        result = f"当前时间：{formatted_time}。"

        # 完成日志记录
        logger.complete_step(step_id, {
            "timestamp": formatted_time,
            "result": result
        })

        return result
    except Exception as e:
        logger.complete_step(step_id, error=str(e))
        raise

# 定义工具的function schema，添加think_and_plan工具
tools = [{
    "type": "function",
    "function": {
        "name": "think_and_plan",
        "description": "在执行任何业务操作前必须调用的思考工具。像人类一样先思考再行动。",
        "parameters": {
            "type": "object",
            "properties": {
                "user_intent": {
                    "type": "string",
                    "description": "你对用户核心需求的理解"
                },
                "current_situation": {
                    "type": "string",
                    "description": "当前状态分析，包括已有信息和缺失信息"
                },
                "plan": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "详细的执行步骤列表，每步都要具体可执行"
                },
                "next_action": {
                    "type": "string",
                    "description": "基于规划确定的下一个具体行动"
                }
            },
            "required": ["user_intent", "current_situation", "plan", "next_action"]
        }
    }
}, {
    "type": "function",
    "function": {
        "name": "get_current_time",
        "description": "当你想知道现在的时间时非常有用。",
    }
}, {
    "type": "function",
    "function": {
        "name": "get_current_weather",
        "description": "当你想查询指定城市的天气时非常有用。",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "城市或县区，比如北京市、杭州市、余杭区等。",
                }
            },
            "required": ["location"]
        }
    }
}, {
    "type": "function",
    "function": {
        "name": "tavily_search",
        "description": "使用Tavily搜索引擎搜索最新信息，支持实时网络搜索和AI生成答案摘要",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索查询关键词"
                },
                "max_results": {
                    "type": "integer",
                    "description": "返回结果数量，默认5个",
                    "default": 5,
                    "minimum": 1,
                    "maximum": 20
                },
                "include_answer": {
                    "type": "boolean",
                    "description": "是否包含AI生成的答案摘要，默认True",
                    "default": True
                }
            },
            "required": ["query"]
        }
    }
}]

# 函数映射
tool_mapping = {
    "think_and_plan": think_and_plan,
    "get_current_time": get_current_time,
    "get_current_weather": get_current_weather,
    "tavily_search": tavily_search
}

# assistant messages模板
assistant_messages_template = {
    "content": "",
    "refusal": None,
    "role": "assistant",
    "audio": None,
    "function_call": None,
    "tool_calls": [{
        "id": "call_xxx",
        "function": {
            "arguments": "",
            "name": "",
        },
        "type": "function",
        "index": 0,
    }],
}

# 异步函数调用函数
async def function_calling(query: str) -> tuple[str, str, str, list, str]:
    """函数调用函数，采用流式输出，兼容普通问答
    
    Args:
        query (str): 用户输入的query
    
    Returns:
        function_name (str): 工具名称
        function_arguments (str): 工具入参
        fun_id (str): 工具ID
        origin_messages (list): 原始消息
        response_content (str): 回答
    """
    origin_messages = [{
        "role": "system",
        "content": """# Agent核心工作流程

你是一个具备规划能力的AI助手。你的工作流程是：

1. **强制规划优先**：在调用任何业务工具前，必须先调用`think_and_plan`工具进行思考
2. **循环执行**：规划 → 执行 → 分析结果 → 重新规划，直到任务完成
3. **错误处理**：工具调用失败时，必须重新规划而不是盲目重试
4. **并行限制**：除非明确确认无依赖关系，否则禁止并行调用工具

记住：你不是一个急性子的实习生，而是一个深思熟虑的专业助手。"""
    }, {
        "role": "user",
        "content": query
    }]

    response = await client.chat.completions.create(
        model="gpt-4o",
        messages=origin_messages,
        tools=tools,
        tool_choice="auto",
        stream=True
    )
    
    function_name = ""
    function_arguments = ""
    response_content = ""
    fun_id = None
    first_chunk = True
    
    async for chunk in response:
        if chunk.choices[0].delta.tool_calls:
            if first_chunk:
                function_name = chunk.choices[0].delta.tool_calls[0].function.name
                function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
                fun_id = chunk.choices[0].delta.tool_calls[0].id
                first_chunk = False
            else:
                if chunk.choices[0].delta.tool_calls[0].function.arguments:
                    function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
        else:
            if chunk.choices[0].delta.content:
                response_content += chunk.choices[0].delta.content
                print(chunk.choices[0].delta.content, end="", flush=True)

    return function_name, function_arguments, fun_id, origin_messages, response_content

# 改进的ReAct工作流程管理器
class ReactWorkflowManager:
    def __init__(self, max_iterations=10):
        self.max_iterations = max_iterations
        self.current_iteration = 0
        self.conversation_history = []

    async def execute_tool_call(self, function_name: str, function_arguments: str, fun_id: str) -> str:
        """安全执行工具调用"""
        try:
            if function_name not in tool_mapping:
                return f"错误：未知工具 {function_name}"

            function = tool_mapping[function_name]
            function_arguments_dict = json.loads(function_arguments)
            result = await function(**function_arguments_dict)

            print(f"✅ 工具执行成功：{function_name}")
            return str(result)

        except Exception as e:
            error_msg = f"❌ 工具执行失败：{function_name}, 错误：{str(e)}"
            print(error_msg)
            return error_msg

    def update_conversation_history(self, function_name: str, function_arguments: str,
                                  fun_id: str, function_result: str, messages: list):
        """更新对话历史 - 修复tool_call_id匹配问题"""
        # 构建正确的assistant消息，包含工具调用
        assistant_message = {
            "role": "assistant",
            "content": None,
            "tool_calls": [{
                "id": fun_id,
                "type": "function",
                "function": {
                    "name": function_name,
                    "arguments": function_arguments
                }
            }]
        }

        # 构建工具响应消息
        tool_message = {
            "role": "tool",
            "content": function_result,
            "tool_call_id": fun_id
        }

        messages.append(assistant_message)
        messages.append(tool_message)

        return messages

# 主函数 - 真正的ReAct循环工作流程
async def main():
    # 用户问题 - 测试更复杂的多步骤查询
    query = "今天北京的天气怎么样？另外帮我查一下AI Agent的最新发展趋势"

    print(f"🔍 用户问题: {query}")
    print("="*80)
    print("🚀 启动真正的ReAct循环工作流程...")
    print("="*80)

    workflow_manager = ReactWorkflowManager()
    messages = [{
        "role": "system",
        "content": """# Agent核心工作流程

你是一个具备规划能力的AI助手。你的工作流程是：

1. **强制规划优先**：在调用任何业务工具前，必须先调用`think_and_plan`工具进行思考
2. **循环执行**：规划 → 执行 → 分析结果 → 重新规划，直到任务完成
3. **错误处理**：工具调用失败时，必须重新规划而不是盲目重试
4. **并行限制**：除非明确确认无依赖关系，否则禁止并行调用工具

记住：你不是一个急性子的实习生，而是一个深思熟虑的专业助手。"""
    }, {
        "role": "user",
        "content": query
    }]

    # ReAct循环：持续执行直到任务完成或达到最大迭代次数
    while workflow_manager.current_iteration < workflow_manager.max_iterations:
        workflow_manager.current_iteration += 1
        print(f"\n🔄 ReAct循环 - 第 {workflow_manager.current_iteration} 轮")
        print("-" * 60)

        # 调用大模型获取下一步行动
        response = await client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            tools=tools,
            tool_choice="auto",
            stream=True
        )

        # 解析响应
        function_name = ""
        function_arguments = ""
        fun_id = None
        response_content = ""
        first_chunk = True

        async for chunk in response:
            if chunk.choices[0].delta.tool_calls:
                if first_chunk:
                    function_name = chunk.choices[0].delta.tool_calls[0].function.name
                    function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
                    fun_id = chunk.choices[0].delta.tool_calls[0].id
                    first_chunk = False
                else:
                    if chunk.choices[0].delta.tool_calls[0].function.arguments:
                        function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
            else:
                if chunk.choices[0].delta.content:
                    response_content += chunk.choices[0].delta.content
                    print(chunk.choices[0].delta.content, end="", flush=True)

        # 如果有工具调用，执行工具
        if function_name:
            print(f"\n🔧 执行工具：{function_name}")
            print(f"📝 参数：{function_arguments}")

            # 验证第一次调用是否为规划工具
            if workflow_manager.current_iteration == 1 and function_name != "think_and_plan":
                print("⚠️  警告：第一次调用应该是think_and_plan工具！")
                print("🔄 强制插入规划步骤...")
                # 这里可以强制插入规划步骤

            # 执行工具
            function_result = await workflow_manager.execute_tool_call(
                function_name, function_arguments, fun_id
            )

            # 更新对话历史
            messages = workflow_manager.update_conversation_history(
                function_name, function_arguments, fun_id, function_result, messages
            )

        else:
            # 没有工具调用，说明任务可能已完成
            print(f"\n✅ 任务完成！最终回答：")
            print(response_content)
            break

    if workflow_manager.current_iteration >= workflow_manager.max_iterations:
        print(f"\n⚠️  达到最大迭代次数 ({workflow_manager.max_iterations})，停止执行")

    # 显示最终的执行统计
    print(f"\n{logger.get_progress_summary()}")
    print(f"\n📁 详细日志已保存到: {logger.dev_log_file}")
    print(f"📊 步骤数据已保存到: {logger.step_log_file}")

if __name__ == "__main__":
    print("🚀 启动集成双层日志系统的ReAct Agent")
    print("=" * 60)
    asyncio.run(main())
