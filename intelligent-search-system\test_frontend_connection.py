#!/usr/bin/env python3
"""
测试前端连接问题
"""

import gradio as gr
import requests
import json

def test_local_api():
    """测试本地API连接"""
    try:
        print("测试本地API连接...")
        response = requests.get("http://127.0.0.1:8000/api/v1/health", timeout=5)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        return f"✅ 本地API连接成功\n状态码: {response.status_code}\n内容: {response.text}"
    except Exception as e:
        print(f"本地API连接失败: {e}")
        return f"❌ 本地API连接失败: {e}"

def test_search_api(query):
    """测试搜索API"""
    if not query.strip():
        return "请输入搜索关键词"
    
    try:
        print(f"测试搜索API: {query}")
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/search",
            json={
                "query": query,
                "max_results": 2,
                "use_react_mode": True,
                "include_answer": True
            },
            timeout=30
        )
        
        print(f"搜索响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = f"✅ 搜索成功!\n"
            result += f"查询: {data.get('query')}\n"
            result += f"答案: {data.get('answer', '无答案')[:200]}...\n"
            result += f"结果数量: {data.get('total_results', 0)}\n"
            result += f"执行时间: {data.get('execution_time_ms', 0)}ms"
            return result
        else:
            return f"❌ 搜索失败\n状态码: {response.status_code}\n错误: {response.text}"
            
    except Exception as e:
        print(f"搜索API异常: {e}")
        return f"❌ 搜索异常: {e}"

# 创建简单的测试界面
with gr.Blocks(title="前端连接测试") as app:
    gr.Markdown("# 前端连接测试\n测试前端是否能正确连接到后端API")
    
    with gr.Row():
        test_btn = gr.Button("测试API连接", variant="primary")
        api_status = gr.Textbox(label="API连接状态", lines=5, interactive=False)
    
    with gr.Row():
        query_input = gr.Textbox(label="测试搜索", placeholder="输入搜索关键词")
        search_btn = gr.Button("测试搜索", variant="secondary")
    
    search_output = gr.Textbox(label="搜索结果", lines=8, interactive=False)
    
    # 绑定事件
    test_btn.click(fn=test_local_api, outputs=[api_status])
    search_btn.click(fn=test_search_api, inputs=[query_input], outputs=[search_output])

if __name__ == "__main__":
    print("启动前端连接测试...")
    app.launch(
        server_name="127.0.0.1",
        server_port=7861,
        share=True,  # 创建公共链接
        inbrowser=True
    )
