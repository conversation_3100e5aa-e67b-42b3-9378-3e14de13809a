#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Gradio前端界面
"""

import gradio as gr
import requests
import json
import os
from datetime import datetime

def search_with_tavily(query: str, max_results: int = 5) -> tuple:
    """直接使用Tavily搜索"""
    if not query.strip():
        return "❌ 请输入搜索关键词", "", ""
    
    try:
        # 使用Tavily API
        tavily_api_key = os.getenv("TAVILY_API_KEY", "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")
        api_url = "https://api.tavily.com/search"
        
        payload = {
            "api_key": tavily_api_key,
            "query": query,
            "max_results": max_results,
            "include_answer": True,
            "search_depth": "basic"
        }
        
        response = requests.post(api_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # AI答案
            ai_answer = data.get("answer", "暂无AI生成的答案")
            if ai_answer:
                ai_answer_formatted = f"""
# 🤖 AI智能答案

{ai_answer}

---
**🔍 搜索查询**: {data.get('query', query)}
**⏰ 搜索时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            else:
                ai_answer_formatted = "🤖 暂无AI生成的答案"
            
            # 搜索结果
            results = data.get("results", [])
            if results:
                results_formatted = []
                for i, result in enumerate(results, 1):
                    results_formatted.append(f"""
### 📄 结果 {i}: {result.get('title', '无标题')}
**🔗 链接**: [{result.get('url', '无链接')}]({result.get('url', '#')})
**📊 相关度**: {result.get('score', 0):.2f}
**📝 摘要**: {result.get('content', '无摘要')[:200]}...
---
""")
                search_results_formatted = "\n".join(results_formatted)
            else:
                search_results_formatted = "❌ 没有找到相关结果"
            
            # 执行统计
            stats = f"""
# 📊 搜索统计

- **搜索查询**: {data.get('query', query)}
- **结果数量**: {len(results)}个
- **搜索时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **API状态**: ✅ 成功
"""
            
            return ai_answer_formatted, search_results_formatted, stats
            
        else:
            error_msg = f"❌ API调用失败: HTTP {response.status_code}"
            return error_msg, "", f"错误信息: {response.text}"
            
    except Exception as e:
        error_msg = f"❌ 搜索失败: {str(e)}"
        return error_msg, "", f"错误详情: {str(e)}"

def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(
        title="🔍 智能搜索系统",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .search-header {
            text-align: center;
            padding: 20px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        """
    ) as interface:
        
        # 标题和说明
        gr.HTML("""
        <div class="search-header">
            <h1>🔍 智能搜索系统</h1>
            <p>基于Tavily API的智能搜索，支持AI答案生成</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # 搜索输入区域
                gr.Markdown("## 🎯 搜索配置")
                
                query_input = gr.Textbox(
                    label="🔍 搜索关键词",
                    placeholder="请输入您要搜索的内容...",
                    lines=2
                )
                
                max_results = gr.Slider(
                    label="📊 结果数量",
                    minimum=1,
                    maximum=10,
                    value=5,
                    step=1
                )
                
                search_btn = gr.Button(
                    "🚀 开始搜索",
                    variant="primary",
                    size="lg"
                )
                
                # 示例查询
                gr.Markdown("### 💡 示例查询")
                examples = gr.Examples(
                    examples=[
                        ["AI Agent最新发展趋势"],
                        ["黑神话悟空发售时间"],
                        ["Python FastAPI教程"],
                        ["机器学习算法对比"],
                        ["区块链技术应用"]
                    ],
                    inputs=[query_input]
                )
            
            with gr.Column(scale=2):
                # 结果显示区域
                gr.Markdown("## 📋 搜索结果")
                
                with gr.Tabs():
                    with gr.TabItem("🤖 AI智能答案"):
                        ai_answer_output = gr.Markdown(
                            value="等待搜索结果...",
                            height=400
                        )
                    
                    with gr.TabItem("📄 详细结果"):
                        search_results_output = gr.Markdown(
                            value="等待搜索结果...",
                            height=400
                        )
                    
                    with gr.TabItem("📊 搜索统计"):
                        stats_output = gr.Markdown(
                            value="等待执行统计...",
                            height=400
                        )
        
        # 绑定搜索事件
        search_btn.click(
            fn=search_with_tavily,
            inputs=[query_input, max_results],
            outputs=[ai_answer_output, search_results_output, stats_output]
        )
        
        # 回车键搜索
        query_input.submit(
            fn=search_with_tavily,
            inputs=[query_input, max_results],
            outputs=[ai_answer_output, search_results_output, stats_output]
        )
        
        # 页脚信息
        gr.HTML("""
        <div style="text-align: center; padding: 20px; color: #666;">
            <p>🔧 技术栈: Gradio + Tavily Search API</p>
            <p>📚 支持AI答案生成和智能搜索</p>
        </div>
        """)
    
    return interface

if __name__ == "__main__":
    print("🚀 启动智能搜索前端界面")
    print("🌐 前端地址: http://localhost:7860")
    print("=" * 50)
    
    # 创建并启动界面
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
