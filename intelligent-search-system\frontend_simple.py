#!/usr/bin/env python3
"""
简化版前端 - 使用Gradio 4.44.1稳定版本
"""

import gradio as gr
import requests
import json
from datetime import datetime

def search_api(query, max_results=5):
    """调用搜索API"""
    if not query.strip():
        return "请输入搜索关键词", ""

    try:
        print(f"搜索查询: {query}")

        # 调用API
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/search",
            json={
                "query": query,
                "max_results": max_results,
                "use_react_mode": True,
                "include_answer": True
            },
            timeout=60
        )

        if response.status_code == 200:
            data = response.json()

            # 格式化结果
            if data.get("success"):
                result_text = f"查询: {data['query']}\n\n"
                result_text += f"AI总结: {data.get('answer', '无总结')}\n\n"
                result_text += f"找到 {data.get('total_results', 0)} 条结果 (耗时: {data.get('execution_time_ms', 0)}ms)\n\n"

                results_detail = "详细结果:\n\n"
                for i, item in enumerate(data.get('results', []), 1):
                    results_detail += f"{i}. {item.get('title', '无标题')}\n"
                    results_detail += f"链接: {item.get('url', '无链接')}\n"
                    results_detail += f"内容: {item.get('content', '无内容')[:500]}...\n"
                    results_detail += f"相关度: {item.get('score', 0):.3f}\n\n"

                return result_text, results_detail
            else:
                return f"搜索失败: {data.get('error', '未知错误')}", ""
        else:
            return f"API请求失败: HTTP {response.status_code}\n{response.text}", ""

    except requests.exceptions.Timeout:
        return "请求超时，请稍后重试", ""
    except requests.exceptions.ConnectionError:
        return "无法连接到API服务，请确保API服务正在运行", ""
    except Exception as e:
        return f"搜索出错: {str(e)}", ""

def test_api():
    """测试API连接"""
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return f"API服务正常\n状态: {data.get('status')}\n时间: {data.get('timestamp')}"
        else:
            return f"API响应异常: {response.status_code}"
    except Exception as e:
        return f"API连接失败: {e}"

# 创建Gradio界面
with gr.Blocks(title="智能搜索系统") as app:

    gr.Markdown("# 智能搜索系统\n基于AI的智能搜索引擎，提供网络搜索和智能总结功能")

    with gr.Row():
        with gr.Column(scale=2):
            query_input = gr.Textbox(
                label="搜索查询",
                placeholder="输入您想搜索的内容，例如：人工智能的发展趋势",
                lines=2
            )

            with gr.Row():
                search_btn = gr.Button("开始搜索", variant="primary")
                clear_btn = gr.Button("清空")
                test_btn = gr.Button("测试连接")

            max_results = gr.Slider(
                label="最大结果数",
                minimum=1,
                maximum=10,
                value=5,
                step=1
            )

        with gr.Column(scale=1):
            api_status = gr.Textbox(
                label="API状态",
                lines=3,
                interactive=False
            )

    with gr.Row():
        with gr.Column():
            summary_output = gr.Textbox(
                label="搜索摘要",
                lines=8,
                interactive=False
            )

        with gr.Column():
            details_output = gr.Textbox(
                label="详细结果",
                lines=8,
                interactive=False
            )

    # 绑定事件
    search_btn.click(
        fn=search_api,
        inputs=[query_input, max_results],
        outputs=[summary_output, details_output]
    )

    clear_btn.click(
        fn=lambda: ("", "", ""),
        outputs=[query_input, summary_output, details_output]
    )

    test_btn.click(
        fn=test_api,
        outputs=[api_status]
    )

if __name__ == "__main__":
    print("启动智能搜索前端...")
    print("API地址: http://127.0.0.1:8000")
    print("前端地址: http://127.0.0.1:7860")

    # 强制本地启动，不使用share模式
    try:
        app.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            inbrowser=True,
            show_error=True,
            quiet=False
        )
    except Exception as e:
        print(f"启动失败: {e}")
        print("尝试使用不同端口...")
        try:
            app.launch(
                server_name="127.0.0.1",
                server_port=7861,
                share=False,
                inbrowser=True
            )
        except Exception as e2:
            print(f"仍然失败: {e2}")
            print("请检查网络设置或防火墙配置")
