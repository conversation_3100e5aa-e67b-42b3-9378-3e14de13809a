import ollama
import sys
import requests

def test_ollama_connection():
    """测试Ollama连接和基本功能"""

    # 首先测试HTTP连接
    print("0. 测试HTTP连接...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        print(f"✓ HTTP连接成功，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ HTTP连接失败: {e}")
        return False

    try:
        print("1. 测试连接到Ollama服务...")

        # 尝试使用显式客户端
        client = ollama.Client(host='http://localhost:11434')
        models = client.list()
        print(f"✓ 连接成功，找到 {len(models['models'])} 个模型")
        
        # 列出可用模型
        print("\n2. 可用模型:")
        for model in models['models']:
            print(f"   - {model['name']}")
        
        # 测试简单的文本聊天
        print("\n3. 测试文本聊天...")
        response = client.chat(
            model='qwen2.5vl:7b',
            messages=[
                {
                    'role': 'user',
                    'content': '你好，请简单回复一句话'
                }
            ]
        )
        print(f"✓ 文本聊天成功: {response['message']['content'][:50]}...")
        
        return True
        
    except ollama.ResponseError as e:
        print(f"✗ Ollama API错误: {e.error}")
        print(f"   状态码: {e.status_code}")
        return False
    except Exception as e:
        print(f"✗ 连接错误: {e}")
        print(f"   错误类型: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = test_ollama_connection()
    if not success:
        print("\n建议:")
        print("1. 确保Ollama服务正在运行")
        print("2. 检查端口11434是否可用")
        print("3. 尝试重启Ollama服务")
        sys.exit(1)
    else:
        print("\n✓ 所有测试通过，可以继续使用多模态功能")
