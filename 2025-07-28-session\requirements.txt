# 智能搜索应用依赖包
# FastAPI后端框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Gradio前端框架
gradio>=4.0.0

# HTTP请求库
requests>=2.31.0

# 数据验证和序列化
pydantic>=2.0.0

# 异步支持
asyncio-mqtt>=0.13.0

# 日志处理
loguru>=0.7.0

# 环境变量管理
python-dotenv>=1.0.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# JSON处理增强
orjson>=3.9.0

# 时间处理
python-dateutil>=2.8.0

# 类型提示支持
typing-extensions>=4.8.0

# CORS支持（FastAPI已包含，但明确列出）
# python-multipart>=0.0.6

# 可选：性能监控
# prometheus-client>=0.17.0

# 可选：缓存支持
# redis>=5.0.0

# 开发工具（可选）
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0
