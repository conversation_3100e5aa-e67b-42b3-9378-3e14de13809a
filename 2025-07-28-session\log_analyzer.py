#!/usr/bin/env python3
"""
日志分析工具 - 帮助开发者快速定位问题
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict

class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = Path(logs_dir)
    
    def list_sessions(self) -> List[str]:
        """列出所有会话"""
        sessions = []
        if self.logs_dir.exists():
            for file in self.logs_dir.glob("steps_*.json"):
                session_id = file.stem.replace("steps_", "")
                sessions.append(session_id)
        return sorted(sessions, reverse=True)
    
    def analyze_session(self, session_id: str) -> Dict:
        """分析指定会话"""
        steps_file = self.logs_dir / f"steps_{session_id}.json"
        
        if not steps_file.exists():
            return {"error": f"会话 {session_id} 不存在"}
        
        with open(steps_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        analysis = {
            "session_info": {
                "session_id": session_id,
                "total_steps": data["total_steps"],
                "completed_steps": data["completed_steps"],
                "failed_steps": data["failed_steps"],
                "success_rate": f"{(data['completed_steps']/data['total_steps']*100):.1f}%" if data["total_steps"] > 0 else "0%"
            },
            "performance": self._analyze_performance(data["steps"]),
            "failures": self._analyze_failures(data["steps"]),
            "step_details": data["steps"]
        }
        
        return analysis
    
    def _analyze_performance(self, steps: List[Dict]) -> Dict:
        """分析性能数据"""
        durations = [step["duration_ms"] for step in steps if step["duration_ms"] > 0]
        
        if not durations:
            return {"total_time": 0, "avg_time": 0, "slowest_step": None}
        
        total_time = sum(durations)
        avg_time = total_time / len(durations)
        slowest_step = max(steps, key=lambda x: x["duration_ms"])
        
        return {
            "total_time_ms": total_time,
            "avg_time_ms": round(avg_time, 2),
            "slowest_step": {
                "name": slowest_step["step_name"],
                "duration_ms": slowest_step["duration_ms"]
            }
        }
    
    def _analyze_failures(self, steps: List[Dict]) -> List[Dict]:
        """分析失败步骤"""
        failures = []
        for step in steps:
            if step["status"] == "FAILED":
                failures.append({
                    "step_id": step["step_id"],
                    "step_name": step["step_name"],
                    "error": step["error_info"],
                    "input_data": step["input_data"]
                })
        return failures
    
    def generate_report(self, session_id: str = None) -> str:
        """生成分析报告"""
        if session_id is None:
            sessions = self.list_sessions()
            if not sessions:
                return "❌ 没有找到任何日志文件"
            session_id = sessions[0]  # 使用最新的会话
        
        analysis = self.analyze_session(session_id)
        
        if "error" in analysis:
            return f"❌ {analysis['error']}"
        
        info = analysis["session_info"]
        perf = analysis["performance"]
        failures = analysis["failures"]
        
        report = f"""
# 📊 ReAct Agent 执行分析报告

## 🔍 会话概览
- **会话ID**: {info['session_id']}
- **总步骤数**: {info['total_steps']}
- **成功步骤**: {info['completed_steps']}
- **失败步骤**: {info['failed_steps']}
- **成功率**: {info['success_rate']}

## ⚡ 性能分析
- **总耗时**: {perf['total_time_ms']}ms
- **平均耗时**: {perf['avg_time_ms']}ms
- **最慢步骤**: {perf['slowest_step']['name']} ({perf['slowest_step']['duration_ms']}ms)

## 📋 步骤详情
"""
        
        for i, step in enumerate(analysis["step_details"], 1):
            status_icon = "✅" if step["status"] == "COMPLETED" else "❌" if step["status"] == "FAILED" else "🔄"
            report += f"""
### {i}. {status_icon} {step['step_name']}
- **状态**: {step['status']}
- **耗时**: {step['duration_ms']}ms
- **开始时间**: {step['start_time']}
"""
            if step["error_info"]:
                report += f"- **错误**: {step['error_info']}\n"
        
        if failures:
            report += f"\n## ❌ 失败分析\n"
            for failure in failures:
                report += f"""
### {failure['step_name']}
- **错误信息**: {failure['error']}
- **输入数据**: {json.dumps(failure['input_data'], ensure_ascii=False, indent=2)}
"""
        
        return report
    
    def find_common_failures(self) -> Dict:
        """查找常见失败模式"""
        sessions = self.list_sessions()
        failure_patterns = {}
        
        for session_id in sessions:
            analysis = self.analyze_session(session_id)
            if "error" not in analysis:
                for failure in analysis["failures"]:
                    error_type = failure["error"].split(":")[0] if ":" in failure["error"] else failure["error"]
                    if error_type not in failure_patterns:
                        failure_patterns[error_type] = []
                    failure_patterns[error_type].append({
                        "session_id": session_id,
                        "step_name": failure["step_name"]
                    })
        
        return failure_patterns

def main():
    """主函数 - 命令行工具"""
    analyzer = LogAnalyzer()
    
    print("🔍 ReAct Agent 日志分析工具")
    print("=" * 50)
    
    sessions = analyzer.list_sessions()
    if not sessions:
        print("❌ 没有找到任何日志文件")
        return
    
    print(f"📁 找到 {len(sessions)} 个会话:")
    for i, session in enumerate(sessions[:5], 1):  # 显示最近5个
        print(f"  {i}. {session}")
    
    print("\n" + "=" * 50)
    
    # 分析最新会话
    latest_session = sessions[0]
    print(f"📊 分析最新会话: {latest_session}")
    
    report = analyzer.generate_report(latest_session)
    print(report)
    
    # 查找常见失败模式
    common_failures = analyzer.find_common_failures()
    if common_failures:
        print("\n" + "=" * 50)
        print("🔍 常见失败模式:")
        for error_type, occurrences in common_failures.items():
            print(f"  ❌ {error_type}: {len(occurrences)} 次")

if __name__ == "__main__":
    main()
