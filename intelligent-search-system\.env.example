# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# Tavily搜索API配置
TAVILY_API_KEY=your_tavily_api_key_here

# 应用配置
APP_NAME=Intelligent Search System
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 服务器配置
API_HOST=0.0.0.0
API_PORT=8000
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=7860

# 数据库配置（如果需要）
# DATABASE_URL=sqlite:///./search_system.db

# Redis配置（如果需要缓存）
# REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_DIR=logs
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# 搜索配置
DEFAULT_MAX_RESULTS=5
DEFAULT_TIMEOUT=30
ENABLE_REACT_MODE=true

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:7860
CORS_METHODS=GET,POST,PUT,DELETE
CORS_HEADERS=*
