# 多模态测试项目依赖包

# === Ollama本地多模态分析 ===
ollama>=0.3.0                # Ollama Python客户端

# === Marker文档处理 ===
marker-pdf[full]>=0.2.0      # PDF/DOCX转Markdown，包含完整功能
# 注意：marker-pdf[full]会自动安装以下依赖：
# - torch
# - torchvision  
# - transformers
# - detectron2
# - layoutparser

# === OpenAI API（用于文档处理器的图片分析） ===
openai>=1.0.0                # OpenAI API客户端

# === 图片处理 ===
Pillow>=10.0.0               # 图片处理和格式验证
opencv-python>=4.8.0         # 图片处理（可选，用于高级图片操作）

# === 文件处理 ===
python-docx>=0.8.11          # DOCX文件处理
PyPDF2>=3.0.0               # PDF文件处理（备用）

# === 数据处理 ===
pandas>=2.0.0                # 数据分析（可选）
numpy>=1.24.0                # 数值计算

# === 用户界面增强 ===
tqdm>=4.65.0                 # 进度条显示
rich>=13.0.0                 # 美化终端输出
colorama>=0.4.6              # 跨平台彩色终端输出

# === HTTP请求 ===
requests>=2.28.0             # HTTP请求（用于下载图片等）
httpx>=0.24.0                # 异步HTTP客户端（可选）

# === 配置管理 ===
pyyaml>=6.0                  # YAML配置文件支持
python-dotenv>=1.0.0         # 环境变量管理

# === 日志和调试 ===
loguru>=0.7.0                # 增强的日志库（可选）

# === 开发和测试依赖（可选） ===
pytest>=7.0.0               # 单元测试
pytest-asyncio>=0.21.0      # 异步测试支持
black>=23.0.0                # 代码格式化
flake8>=6.0.0                # 代码检查
mypy>=1.0.0                  # 类型检查

# === 性能监控（可选） ===
psutil>=5.9.0                # 系统资源监控
memory-profiler>=0.61.0      # 内存使用分析

# === 数据存储（可选） ===
sqlite3                      # SQLite数据库（Python内置）
# 如果需要其他数据库：
# pymongo>=4.0.0             # MongoDB
# redis>=4.5.0               # Redis

# 安装说明：
# 1. 基础安装：pip install ollama pillow tqdm rich requests
# 2. 完整安装：pip install -r requirements.txt
# 3. 开发环境：pip install -r requirements.txt（包含所有依赖）

# 注意事项：
# - marker-pdf[full] 需要较长时间安装，包含深度学习模型
# - 如果只使用Ollama功能，可以只安装：ollama pillow tqdm rich
# - GPU支持需要正确安装CUDA和对应的PyTorch版本
# - 某些依赖可能需要系统级别的库支持（如OpenCV）
