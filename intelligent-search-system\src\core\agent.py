"""
ReAct Agent核心模块

实现基于推理-行动模式的智能代理。
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from openai import AsyncOpenAI

from .search import SearchEngine
from .planning import PlanningEngine
from ..utils.config import get_settings
from ..utils.logging import DualLogger
from ..utils.exceptions import AgentError, ConfigurationError


class ReactAgent:
    """ReAct模式的智能代理"""
    
    def __init__(self, logger: Optional[DualLogger] = None):
        self.settings = get_settings()
        self.logger = logger or DualLogger()
        self.search_engine = SearchEngine(self.logger)
        self.planning_engine = PlanningEngine(self.logger)
        
        # 初始化OpenAI客户端
        if not self.settings.openai_api_key:
            raise ConfigurationError("OpenAI API密钥未配置")
            
        self.client = AsyncOpenAI(
            api_key=self.settings.openai_api_key,
            base_url=self.settings.openai_base_url
        )
        
        # 工具映射
        self.tool_mapping = {
            "think_and_plan": self.planning_engine.think_and_plan_to_json,
            "tavily_search": self.search_engine.search_to_json,
            "get_current_time": self._get_current_time
        }
        
        # 工具定义
        self.tools = self._define_tools()
    
    def _define_tools(self) -> List[Dict[str, Any]]:
        """定义可用工具"""
        return [{
            "type": "function",
            "function": {
                "name": "think_and_plan",
                "description": "在执行任何业务操作前必须调用的思考工具。像人类一样先思考再行动。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_intent": {
                            "type": "string",
                            "description": "你对用户核心需求的理解"
                        },
                        "current_situation": {
                            "type": "string",
                            "description": "当前状态分析，包括已有信息和缺失信息"
                        },
                        "plan": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "详细的执行步骤列表，每步都要具体可执行"
                        },
                        "next_action": {
                            "type": "string",
                            "description": "基于规划确定的下一个具体行动"
                        }
                    },
                    "required": ["user_intent", "current_situation", "plan", "next_action"]
                }
            }
        }, {
            "type": "function",
            "function": {
                "name": "tavily_search",
                "description": "使用Tavily搜索引擎搜索最新信息，支持实时网络搜索和AI生成答案摘要",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索查询关键词"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "返回结果数量，默认5个",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 20
                        },
                        "include_answer": {
                            "type": "boolean",
                            "description": "是否包含AI生成的答案摘要，默认True",
                            "default": True
                        }
                    },
                    "required": ["query"]
                }
            }
        }, {
            "type": "function",
            "function": {
                "name": "get_current_time",
                "description": "当你想知道现在的时间时非常有用。"
            }
        }]
    
    async def _get_current_time(self) -> str:
        """获取当前时间"""
        from datetime import datetime
        
        step_id = self.logger.start_step("查询当前时间", {})
        
        try:
            current_datetime = datetime.now()
            formatted_time = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
            result = f"当前时间：{formatted_time}。"
            
            self.logger.complete_step(step_id, {
                "timestamp": formatted_time,
                "result": result
            })
            
            return result
        except Exception as e:
            self.logger.complete_step(step_id, error=str(e))
            raise
    
    async def execute_tool_call(
        self, 
        function_name: str, 
        function_arguments: str, 
        fun_id: str
    ) -> str:
        """安全执行工具调用"""
        try:
            if function_name not in self.tool_mapping:
                return f"错误：未知工具 {function_name}"
            
            function = self.tool_mapping[function_name]
            function_arguments_dict = json.loads(function_arguments)
            
            # 根据函数类型调用
            if asyncio.iscoroutinefunction(function):
                result = await function(**function_arguments_dict)
            else:
                result = function(**function_arguments_dict)
            
            print(f"✅ 工具执行成功：{function_name}")
            return str(result)
            
        except Exception as e:
            error_msg = f"❌ 工具执行失败：{function_name}, 错误：{str(e)}"
            print(error_msg)
            return error_msg
    
    def update_conversation_history(
        self, 
        function_name: str, 
        function_arguments: str,
        fun_id: str, 
        function_result: str, 
        messages: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """更新对话历史"""
        # 构建assistant消息，包含工具调用
        assistant_message = {
            "role": "assistant",
            "content": None,
            "tool_calls": [{
                "id": fun_id,
                "type": "function",
                "function": {
                    "name": function_name,
                    "arguments": function_arguments
                }
            }]
        }
        
        # 构建工具响应消息
        tool_message = {
            "role": "tool",
            "content": function_result,
            "tool_call_id": fun_id
        }
        
        messages.append(assistant_message)
        messages.append(tool_message)
        
        return messages
    
    async def function_calling(self, query: str) -> Tuple[str, str, str, List[Dict], str]:
        """
        函数调用函数，采用流式输出
        
        Args:
            query: 用户输入的查询
            
        Returns:
            Tuple[function_name, function_arguments, fun_id, origin_messages, response_content]
        """
        origin_messages = [{
            "role": "system",
            "content": """# Agent核心工作流程

你是一个具备规划能力的AI助手。你的工作流程是：

1. **强制规划优先**：在调用任何业务工具前，必须先调用`think_and_plan`工具进行思考
2. **循环执行**：规划 → 执行 → 分析结果 → 重新规划，直到任务完成
3. **错误处理**：工具调用失败时，必须重新规划而不是盲目重试
4. **并行限制**：除非明确确认无依赖关系，否则禁止并行调用工具

记住：你不是一个急性子的实习生，而是一个深思熟虑的专业助手。"""
        }, {
            "role": "user",
            "content": query
        }]
        
        response = await self.client.chat.completions.create(
            model=self.settings.openai_model,
            messages=origin_messages,
            tools=self.tools,
            tool_choice="auto",
            stream=True
        )
        
        function_name = ""
        function_arguments = ""
        response_content = ""
        fun_id = None
        first_chunk = True
        
        async for chunk in response:
            if chunk.choices[0].delta.tool_calls:
                if first_chunk:
                    function_name = chunk.choices[0].delta.tool_calls[0].function.name
                    function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
                    fun_id = chunk.choices[0].delta.tool_calls[0].id
                    first_chunk = False
                else:
                    if chunk.choices[0].delta.tool_calls[0].function.arguments:
                        function_arguments += chunk.choices[0].delta.tool_calls[0].function.arguments
            else:
                if chunk.choices[0].delta.content:
                    response_content += chunk.choices[0].delta.content
                    print(chunk.choices[0].delta.content, end="", flush=True)
        
        return function_name, function_arguments, fun_id, origin_messages, response_content
