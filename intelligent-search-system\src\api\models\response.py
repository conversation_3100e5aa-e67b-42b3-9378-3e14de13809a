"""
API响应模型

定义API响应的数据结构。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class SearchResultItem(BaseModel):
    """搜索结果项"""
    title: str = Field(..., description="结果标题")
    url: str = Field(..., description="结果URL")
    content: str = Field(..., description="结果内容摘要")
    score: float = Field(default=0.0, description="相关性评分")


class SearchResponse(BaseModel):
    """搜索响应模型"""
    success: bool = Field(..., description="请求是否成功")
    query: str = Field(..., description="原始查询")
    answer: Optional[str] = Field(default=None, description="AI生成的答案")
    results: List[SearchResultItem] = Field(default=[], description="搜索结果列表")
    total_results: int = Field(default=0, description="结果总数")
    execution_time_ms: int = Field(default=0, description="执行时间（毫秒）")
    session_id: Optional[str] = Field(default=None, description="会话ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "query": "什么是人工智能？",
                "answer": "人工智能（AI）是计算机科学的一个分支...",
                "results": [
                    {
                        "title": "人工智能 - 维基百科",
                        "url": "https://zh.wikipedia.org/wiki/人工智能",
                        "content": "人工智能是指由人制造出来的机器所表现出来的智能...",
                        "score": 0.95
                    }
                ],
                "total_results": 5,
                "execution_time_ms": 1500,
                "session_id": "20250728_143022",
                "timestamp": "2025-07-28T14:30:22.123456"
            }
        }


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="服务版本")
    dependencies: Optional[Dict[str, str]] = Field(default=None, description="依赖服务状态")
    uptime_seconds: Optional[int] = Field(default=None, description="运行时间（秒）")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2025-07-28T14:30:22.123456",
                "version": "1.0.0",
                "dependencies": {
                    "openai_api": "healthy",
                    "tavily_api": "healthy"
                },
                "uptime_seconds": 3600
            }
        }


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(default=False, description="请求是否成功")
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间戳")
    request_id: Optional[str] = Field(default=None, description="请求ID")
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "error_code": "SEARCH_ERROR",
                "error_message": "搜索服务暂时不可用",
                "details": {
                    "service": "tavily_api",
                    "status_code": 503
                },
                "timestamp": "2025-07-28T14:30:22.123456",
                "request_id": "req_123456789"
            }
        }
