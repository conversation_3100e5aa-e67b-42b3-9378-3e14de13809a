#!/usr/bin/env python3
"""
测试启动脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    try:
        print("测试模块导入...")
        
        # 测试配置模块
        from src.utils.config import get_settings
        settings = get_settings()
        print(f"✅ 配置模块导入成功")
        print(f"   API端口: {settings.api_port}")
        print(f"   前端端口: {settings.frontend_port}")
        
        # 测试API模块
        from src.api.main import app
        print("✅ API模块导入成功")
        
        # 测试核心模块
        from src.core.agent import ReactAgent
        print("✅ 核心模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_creation():
    """测试API应用创建"""
    try:
        print("\n测试API应用创建...")
        from src.api.main import app
        
        # 检查应用属性
        print(f"✅ FastAPI应用创建成功")
        print(f"   应用标题: {app.title}")
        print(f"   应用版本: {app.version}")
        
        return True
        
    except Exception as e:
        print(f"❌ API应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("智能搜索系统启动测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        sys.exit(1)
    
    # 测试API创建
    if not test_api_creation():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！系统可以正常启动")
    print("=" * 50)
    
    # 显示启动命令
    print("\n启动命令:")
    print("  API服务: python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000")
    print("  前端服务: python src/frontend/app.py")
    print("  统一启动: python scripts/start.py")

if __name__ == "__main__":
    main()
