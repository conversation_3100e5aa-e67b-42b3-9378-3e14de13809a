#!/usr/bin/env python3
"""
智能搜索系统启动脚本

统一启动后端API和前端界面。
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config import get_settings


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.processes: List[subprocess.Popen] = []
        self.running = True
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 收到信号 {signum}，正在关闭服务...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)
    
    def check_dependencies(self) -> bool:
        """检查依赖和配置"""
        print("检查系统依赖和配置...")
        
        # 检查Python版本
        if sys.version_info < (3, 9):
            print("❌ Python版本需要3.9或更高")
            return False
        
        # 检查必要的环境变量
        if not self.settings.openai_api_key:
            print("❌ OPENAI_API_KEY环境变量未设置")
            return False
        
        if not self.settings.tavily_api_key:
            print("❌ TAVILY_API_KEY环境变量未设置")
            return False
        
        # 检查日志目录
        log_dir = Path(self.settings.log_dir)
        log_dir.mkdir(exist_ok=True)
        
        print("✅ 依赖检查通过")
        return True
    
    def start_api_service(self) -> Optional[subprocess.Popen]:
        """启动API服务"""
        print(f"启动API服务 (端口: {self.settings.api_port})...")
        
        try:
            cmd = [
                sys.executable, "-m", "uvicorn",
                "src.api.main:app",
                "--host", self.settings.api_host,
                "--port", str(self.settings.api_port),
                "--log-level", self.settings.log_level.lower()
            ]
            
            if self.settings.debug:
                cmd.append("--reload")
            
            process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 启动日志输出线程
            threading.Thread(
                target=self._log_output,
                args=(process, "API"),
                daemon=True
            ).start()
            
            return process
            
        except Exception as e:
            print(f"❌ API服务启动失败: {e}")
            return None
    
    def start_frontend_service(self) -> Optional[subprocess.Popen]:
        """启动前端服务"""
        print(f"🎨 启动前端服务 (端口: {self.settings.frontend_port})...")
        
        try:
            cmd = [
                sys.executable, "-c",
                f"""
import sys
sys.path.insert(0, '{project_root}')
from src.frontend.app import create_app
app = create_app()
app.launch(
    server_name='{self.settings.frontend_host}',
    server_port={self.settings.frontend_port},
    share=False,
    debug={self.settings.debug}
)
"""
            ]
            
            process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 启动日志输出线程
            threading.Thread(
                target=self._log_output,
                args=(process, "Frontend"),
                daemon=True
            ).start()
            
            return process
            
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return None
    
    def _log_output(self, process: subprocess.Popen, service_name: str):
        """输出服务日志"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip():
                    print(f"[{service_name}] {line.strip()}")
        except Exception:
            pass
    
    def wait_for_service(self, host: str, port: int, timeout: int = 60) -> bool:
        """等待服务启动"""
        import socket
        import requests

        start_time = time.time()
        print(f"   正在检查 {host}:{port}...")

        while time.time() - start_time < timeout:
            try:
                # 首先尝试socket连接
                with socket.create_connection((host, port), timeout=2):
                    # 如果是API服务，尝试HTTP健康检查
                    if port == self.settings.api_port:
                        try:
                            url = f"http://{host}:{port}/api/v1/health"
                            response = requests.get(url, timeout=2)
                            if response.status_code == 200:
                                print(f"   ✅ 服务健康检查通过: {url}")
                                return True
                        except:
                            pass
                    else:
                        # 非API服务，socket连接成功即可
                        print(f"   ✅ 服务端口连接成功: {host}:{port}")
                        return True
            except (socket.error, ConnectionRefusedError):
                elapsed = int(time.time() - start_time)
                print(f"   ⏳ 等待中... ({elapsed}s/{timeout}s)")
                time.sleep(2)

        print(f"   ❌ 服务启动超时: {host}:{port}")
        return False
    
    def stop_all_services(self):
        """停止所有服务"""
        print("🛑 正在停止所有服务...")
        
        for process in self.processes:
            if process.poll() is None:  # 进程仍在运行
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                except Exception as e:
                    print(f"⚠️ 停止进程时出错: {e}")
        
        self.processes.clear()
        print("✅ 所有服务已停止")
    
    def run(self):
        """运行服务管理器"""
        print("智能搜索系统启动器")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            sys.exit(1)
        
        # 启动API服务
        api_process = self.start_api_service()
        if not api_process:
            sys.exit(1)
        self.processes.append(api_process)
        
        # 等待API服务启动
        print("⏳ 等待API服务启动...")
        # 使用localhost进行健康检查，因为0.0.0.0在某些系统上不能直接连接
        check_host = "127.0.0.1" if self.settings.api_host == "0.0.0.0" else self.settings.api_host
        if not self.wait_for_service(check_host, self.settings.api_port):
            print("❌ API服务启动超时")
            self.stop_all_services()
            sys.exit(1)
        
        print("✅ API服务启动成功")
        
        # 启动前端服务
        frontend_process = self.start_frontend_service()
        if frontend_process:
            self.processes.append(frontend_process)
            
            # 等待前端服务启动
            print("⏳ 等待前端服务启动...")
            # 使用localhost进行健康检查，因为0.0.0.0在某些系统上不能直接连接
            check_host = "127.0.0.1" if self.settings.frontend_host == "0.0.0.0" else self.settings.frontend_host
            if self.wait_for_service(check_host, self.settings.frontend_port):
                print("✅ 前端服务启动成功")
            else:
                print("⚠️ 前端服务启动超时，但API服务仍可用")
        
        # 显示访问信息
        print("\n" + "=" * 50)
        print("智能搜索系统启动完成！")
        print(f"API文档: http://{self.settings.api_host}:{self.settings.api_port}/docs")
        print(f"前端界面: http://{self.settings.frontend_host}:{self.settings.frontend_port}")
        print(f"健康检查: http://{self.settings.api_host}:{self.settings.api_port}/api/v1/health")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 保持运行
        try:
            while self.running:
                # 检查进程状态
                for i, process in enumerate(self.processes):
                    if process.poll() is not None:
                        print(f"⚠️ 服务 {i} 意外退出，退出码: {process.returncode}")
                        self.running = False
                        break
                
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all_services()


def main():
    """主函数"""
    manager = ServiceManager()
    manager.run()


if __name__ == "__main__":
    main()
