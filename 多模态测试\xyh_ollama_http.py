import requests
import base64
import json
import os

def load_image_as_base64(image_path):
    """
    加载图片文件并转换为base64编码
    """
    try:
        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
            encoded_image = base64.b64encode(image_data).decode('utf-8')
            return encoded_image
    except FileNotFoundError:
        print(f"错误：找不到图片文件 {image_path}")
        return None
    except Exception as e:
        print(f"错误：读取图片文件时出现问题 - {e}")
        return None

def chat_with_image_http_stream(model, message, image_base64):
    """
    使用HTTP API流式与Ollama多模态模型聊天
    """
    url = "http://localhost:11434/api/chat"

    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": message,
                "images": [image_base64]
            }
        ],
        "stream": True
    }

    try:
        print("发送流式请求到Ollama API...")
        response = requests.post(url, json=payload, stream=True, timeout=120)

        if response.status_code == 200:
            print("\n" + "="*50)
            print("模型回复 (流式输出):")
            print("="*50)

            full_response = ""
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'message' in data and 'content' in data['message']:
                            content = data['message']['content']
                            print(content, end='', flush=True)
                            full_response += content

                        if data.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue

            print("\n" + "="*50)
            return full_response
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None

    except requests.exceptions.Timeout:
        print("请求超时，模型可能需要更长时间处理")
        return None
    except requests.exceptions.ConnectionError:
        print("连接错误，请确保Ollama服务正在运行")
        return None
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def main():
    # 图片文件路径
    image_path = r"J:\LLM-TT\多模态测试\屏幕截图 2025-07-29 164644.jpg"
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：图片文件不存在 - {image_path}")
        return
    
    print(f"加载图片: {image_path}")
    
    # 加载并编码图片
    encoded_image = load_image_as_base64(image_path)
    if encoded_image is None:
        return
    
    print(f"图片编码成功，大小: {len(encoded_image)} 字符")
    
    # 发送到模型
    model = "qwen2.5vl:7b"
    message = "请详细描述这张图片的内容"
    
    print(f"使用模型: {model}")
    print(f"问题: {message}")
    
    result = chat_with_image_http_stream(model, message, encoded_image)

    if not result:
        print("获取回复失败")

if __name__ == "__main__":
    main()
