#!/usr/bin/env python3
"""
双层日志系统的ReAct Agent
- 用户层：简洁进度反馈
- 开发者层：详细执行日志
"""

import os
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    STEP = "STEP"  # 专门用于步骤跟踪

@dataclass
class ExecutionStep:
    """执行步骤的详细记录"""
    step_id: str
    step_name: str
    status: str  # PLANNED, EXECUTING, COMPLETED, FAILED
    start_time: str
    end_time: str = ""
    input_data: Dict[str, Any] = None
    output_data: Dict[str, Any] = None
    error_info: str = ""
    duration_ms: int = 0
    
    def to_dict(self):
        return asdict(self)

class DualLogger:
    """双层日志系统"""
    
    def __init__(self, session_id: str = None):
        self.session_id = session_id or datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 开发者日志文件
        self.dev_log_file = self.log_dir / f"dev_log_{self.session_id}.md"
        self.step_log_file = self.log_dir / f"steps_{self.session_id}.json"
        
        # 执行步骤跟踪
        self.execution_steps: List[ExecutionStep] = []
        self.current_step_id = 0
        
        # 初始化日志文件
        self._init_dev_log()
    
    def _init_dev_log(self):
        """初始化开发者日志文件"""
        header = f"""# ReAct Agent 执行日志
**会话ID**: {self.session_id}  
**开始时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**日志级别**: DEBUG, INFO, WARNING, ERROR, STEP

---

## 📋 执行概览
- ✅ 已完成步骤: 0
- 🔄 当前步骤: 初始化
- ❌ 失败步骤: 0
- ⏱️ 总耗时: 0ms

---

## 📝 详细日志

"""
        with open(self.dev_log_file, 'w', encoding='utf-8') as f:
            f.write(header)
    
    def log(self, level: LogLevel, message: str, data: Dict = None):
        """写入开发者日志"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        log_entry = f"### [{timestamp}] {level.value}\n{message}\n"
        
        if data:
            log_entry += f"```json\n{json.dumps(data, ensure_ascii=False, indent=2)}\n```\n"
        
        log_entry += "\n---\n\n"
        
        with open(self.dev_log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    
    def start_step(self, step_name: str, input_data: Dict = None) -> str:
        """开始一个执行步骤"""
        self.current_step_id += 1
        step_id = f"step_{self.current_step_id:03d}"
        
        step = ExecutionStep(
            step_id=step_id,
            step_name=step_name,
            status="EXECUTING",
            start_time=datetime.now().isoformat(),
            input_data=input_data or {}
        )
        
        self.execution_steps.append(step)
        
        # 记录到开发者日志
        self.log(LogLevel.STEP, f"🚀 开始执行步骤: {step_name}", {
            "step_id": step_id,
            "input_data": input_data
        })
        
        # 用户友好的反馈
        print(f"🔄 正在执行: {step_name}")
        
        return step_id
    
    def complete_step(self, step_id: str, output_data: Dict = None, error: str = None):
        """完成一个执行步骤"""
        step = next((s for s in self.execution_steps if s.step_id == step_id), None)
        if not step:
            return
        
        step.end_time = datetime.now().isoformat()
        step.output_data = output_data or {}
        
        if error:
            step.status = "FAILED"
            step.error_info = error
            self.log(LogLevel.ERROR, f"❌ 步骤失败: {step.step_name}", {
                "step_id": step_id,
                "error": error,
                "output_data": output_data
            })
            print(f"❌ 执行失败: {step.step_name} - {error}")
        else:
            step.status = "COMPLETED"
            self.log(LogLevel.STEP, f"✅ 步骤完成: {step.step_name}", {
                "step_id": step_id,
                "output_data": output_data
            })
            print(f"✅ 已完成: {step.step_name}")
        
        # 计算耗时
        if step.start_time and step.end_time:
            start = datetime.fromisoformat(step.start_time)
            end = datetime.fromisoformat(step.end_time)
            step.duration_ms = int((end - start).total_seconds() * 1000)
        
        # 保存步骤详情到JSON
        self._save_steps_json()
        
        # 更新概览
        self._update_overview()
    
    def _save_steps_json(self):
        """保存步骤详情到JSON文件"""
        steps_data = {
            "session_id": self.session_id,
            "total_steps": len(self.execution_steps),
            "completed_steps": len([s for s in self.execution_steps if s.status == "COMPLETED"]),
            "failed_steps": len([s for s in self.execution_steps if s.status == "FAILED"]),
            "steps": [step.to_dict() for step in self.execution_steps]
        }
        
        with open(self.step_log_file, 'w', encoding='utf-8') as f:
            json.dump(steps_data, f, ensure_ascii=False, indent=2)
    
    def _update_overview(self):
        """更新日志文件的概览部分"""
        completed = len([s for s in self.execution_steps if s.status == "COMPLETED"])
        failed = len([s for s in self.execution_steps if s.status == "FAILED"])
        total_duration = sum(s.duration_ms for s in self.execution_steps if s.duration_ms > 0)
        
        # 读取现有内容
        with open(self.dev_log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新概览部分
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '- ✅ 已完成步骤:' in line:
                lines[i] = f"- ✅ 已完成步骤: {completed}"
            elif '- 🔄 当前步骤:' in line:
                current_step = "完成" if completed == len(self.execution_steps) else f"步骤{len(self.execution_steps)}"
                lines[i] = f"- 🔄 当前步骤: {current_step}"
            elif '- ❌ 失败步骤:' in line:
                lines[i] = f"- ❌ 失败步骤: {failed}"
            elif '- ⏱️ 总耗时:' in line:
                lines[i] = f"- ⏱️ 总耗时: {total_duration}ms"
        
        # 写回文件
        with open(self.dev_log_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
    
    def get_progress_summary(self) -> str:
        """获取用户友好的进度概要"""
        completed = len([s for s in self.execution_steps if s.status == "COMPLETED"])
        total = len(self.execution_steps)
        
        if total == 0:
            return "🚀 准备开始..."
        
        progress_bar = "█" * completed + "░" * (total - completed)
        return f"📊 进度 [{progress_bar}] {completed}/{total} 步完成"

# 全局日志器
logger = DualLogger()

async def enhanced_think_and_plan_with_logging(
    user_intent: str,
    current_situation: str, 
    plan: list,
    next_action: str
) -> str:
    """带日志的增强思考规划工具"""
    
    step_id = logger.start_step("思考与规划", {
        "user_intent": user_intent,
        "current_situation": current_situation,
        "plan": plan,
        "next_action": next_action
    })
    
    try:
        planning_result = {
            "reasoning": {
                "用户意图": user_intent,
                "当前状况": current_situation,
                "执行计划": plan
            },
            "acting": {
                "下一步行动": next_action
            },
            "metadata": {
                "规划时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "模式": "ReAct (Reasoning and Acting)"
            }
        }
        
        logger.complete_step(step_id, {"planning_result": planning_result})
        return json.dumps(planning_result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.complete_step(step_id, error=str(e))
        raise

async def mock_search_with_logging(query: str) -> str:
    """带日志的模拟搜索"""
    step_id = logger.start_step(f"搜索: {query}", {"query": query})
    
    try:
        await asyncio.sleep(1)  # 模拟网络延迟
        result = f"找到关于'{query}'的相关信息：这是模拟的搜索结果。"
        
        logger.complete_step(step_id, {"result": result, "result_length": len(result)})
        return result
        
    except Exception as e:
        logger.complete_step(step_id, error=str(e))
        raise

async def demo_dual_logging():
    """演示双层日志系统"""
    print("=" * 60)
    print("🚀 双层日志系统演示")
    print(f"📁 日志文件: {logger.dev_log_file}")
    print(f"📊 步骤文件: {logger.step_log_file}")
    print("=" * 60)
    
    # 执行一系列操作
    await enhanced_think_and_plan_with_logging(
        user_intent="了解AI发展趋势",
        current_situation="需要搜索最新信息",
        plan=["搜索", "分析", "总结"],
        next_action="开始搜索"
    )
    
    await mock_search_with_logging("AI Agent 2024")
    await mock_search_with_logging("ReAct模式")
    
    print(f"\n{logger.get_progress_summary()}")
    print(f"\n📁 详细日志已保存到: {logger.dev_log_file}")
    print(f"📊 步骤数据已保存到: {logger.step_log_file}")

if __name__ == "__main__":
    asyncio.run(demo_dual_logging())
