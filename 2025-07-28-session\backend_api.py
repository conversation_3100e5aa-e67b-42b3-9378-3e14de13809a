#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI后端服务 - 智能搜索API
集成ReAct工作流程和双层日志系统
"""

import os
import json
import asyncio
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 导入现有的日志系统
from dual_logging_react_agent import DualLogger, ExecutionStep

# 初始化FastAPI应用
app = FastAPI(
    title="智能搜索API",
    description="基于ReAct模式的智能搜索服务，集成双层日志系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局日志器
logger = DualLogger()

# 请求和响应模型
class SearchRequest(BaseModel):
    query: str
    max_results: Optional[int] = 5
    include_answer: Optional[bool] = True
    use_react_mode: Optional[bool] = True

class SearchResult(BaseModel):
    title: str
    url: str
    content: str
    score: float

class SearchResponse(BaseModel):
    query: str
    answer: str
    results: List[SearchResult]
    execution_time: float
    steps_completed: int
    session_id: str

class ProgressUpdate(BaseModel):
    session_id: str
    current_step: str
    progress: str
    completed_steps: int
    total_steps: int
    status: str  # "running", "completed", "error"

# 搜索工具实现
class SearchEngine:
    def __init__(self):
        self.tavily_api_key = os.getenv("TAVILY_API_KEY", "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")
    
    async def tavily_search(self, query: str, max_results: int = 5, include_answer: bool = True) -> Dict:
        """Tavily搜索实现"""
        step_id = logger.start_step(f"Tavily搜索: {query}", {
            "query": query,
            "max_results": max_results,
            "include_answer": include_answer
        })
        
        try:
            api_url = "https://api.tavily.com/search"
            payload = {
                "api_key": self.tavily_api_key,
                "query": query,
                "max_results": max_results,
                "include_answer": include_answer,
                "search_depth": "advanced"
            }
            
            response = requests.post(api_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                formatted_results = {
                    "query": data.get("query", query),
                    "answer": data.get("answer", ""),
                    "results": []
                }
                
                for result in data.get("results", []):
                    formatted_results["results"].append({
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "content": result.get("content", ""),
                        "score": result.get("score", 0.0)
                    })
                
                logger.complete_step(step_id, {
                    "results_count": len(formatted_results["results"]),
                    "has_answer": bool(formatted_results["answer"]),
                    "api_response_code": response.status_code
                })
                
                return formatted_results
            else:
                error_msg = f"Tavily API调用失败: HTTP {response.status_code}"
                logger.complete_step(step_id, error=error_msg)
                raise HTTPException(status_code=500, detail=error_msg)
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            logger.complete_step(step_id, error=error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        except Exception as e:
            error_msg = f"搜索失败: {str(e)}"
            logger.complete_step(step_id, error=error_msg)
            raise HTTPException(status_code=500, detail=error_msg)

    async def think_and_plan(self, user_intent: str, current_situation: str, 
                           plan: List[str], next_action: str) -> Dict:
        """ReAct思考规划"""
        step_id = logger.start_step("思考与规划", {
            "user_intent": user_intent,
            "current_situation": current_situation,
            "plan": plan,
            "next_action": next_action
        })
        
        try:
            planning_result = {
                "reasoning": {
                    "用户意图": user_intent,
                    "当前状况": current_situation,
                    "执行计划": plan
                },
                "acting": {
                    "下一步行动": next_action
                },
                "metadata": {
                    "规划时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "模式": "ReAct (Reasoning and Acting)"
                }
            }
            
            logger.complete_step(step_id, {"planning_result": planning_result})
            return planning_result
            
        except Exception as e:
            logger.complete_step(step_id, error=str(e))
            raise HTTPException(status_code=500, detail=f"规划失败: {str(e)}")

# 初始化搜索引擎
search_engine = SearchEngine()

# 存储活跃会话的进度
active_sessions: Dict[str, ProgressUpdate] = {}

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "智能搜索API服务",
        "version": "1.0.0",
        "endpoints": {
            "search": "/search",
            "progress": "/progress/{session_id}",
            "logs": "/logs/{session_id}"
        }
    }

@app.post("/search", response_model=SearchResponse)
async def search(request: SearchRequest, background_tasks: BackgroundTasks):
    """智能搜索接口"""
    session_id = logger.session_id
    start_time = datetime.now()
    
    try:
        if request.use_react_mode:
            # ReAct模式：先思考规划，再执行搜索
            planning_result = await search_engine.think_and_plan(
                user_intent=f"用户想搜索关于'{request.query}'的信息",
                current_situation="用户提交了搜索请求，需要获取相关信息",
                plan=["使用Tavily搜索引擎获取相关信息", "分析搜索结果", "返回结构化数据"],
                next_action=f"执行搜索查询: {request.query}"
            )
        
        # 执行搜索
        search_result = await search_engine.tavily_search(
            query=request.query,
            max_results=request.max_results,
            include_answer=request.include_answer
        )
        
        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 构建响应
        response = SearchResponse(
            query=search_result["query"],
            answer=search_result["answer"],
            results=[
                SearchResult(**result) for result in search_result["results"]
            ],
            execution_time=execution_time,
            steps_completed=len(logger.execution_steps),
            session_id=session_id
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/progress/{session_id}")
async def get_progress(session_id: str):
    """获取搜索进度"""
    if session_id in active_sessions:
        return active_sessions[session_id]
    else:
        return {"error": "会话不存在"}

@app.get("/logs/{session_id}")
async def get_logs(session_id: str):
    """获取详细日志"""
    try:
        # 返回当前会话的执行步骤
        return {
            "session_id": session_id,
            "steps": [asdict(step) for step in logger.execution_steps],
            "summary": logger.get_progress_summary()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(active_sessions)
    }

if __name__ == "__main__":
    print("🚀 启动智能搜索API服务")
    print("📡 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("=" * 50)
    
    uvicorn.run(
        "backend_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
