{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 以终为始搞懂旋转位置编码\n", "作者：Double童发发\n", "\n", "B站主页：https://space.bilibili.com/323109608\n", "\n", "旋转位置编码的理解通常有两种切入点：\n", "1. 从旋转矩阵的定义出发，推导出旋转位置编码的公式，进而用代码实现。\n", "2. 从复数的角度出发，推导出旋转位置编码的公式，进而用代码实现。\n", "\n", "这两种确实都是理解旋转位置编码的正确方式，然而门槛太高难以掌握，也不利于初学者理解。今天我们换一个角度，**“以终为始”，直接以Qwen2.5的代码入手，从实现逐步引入旋转位置编码的数学理论**。最终你会发现，从代码入手反而简单，厉害有效的东西往往在实现过程中是足够优雅的。\n", "\n", "- 备注：为了便于讲解、测试与复现，会修改部分Qwen2.5的代码，只看最相关的部分\n", "- 参考文件：HuggingFace中`modeling_qwen2.py`文件"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境准备\n", "首先需要安装必要的依赖库，可以通过`pip install`安装，主要包括`PyTorch`、`Numpy`、`Transformers`库，如果已经安装可以跳过这一步。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 必要库导入"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import transformers\n", "from typing import Tuple, Optional, Callable"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 旋转位置编码的实现\n", "### 3.1. <PERSON><PERSON>2R<PERSON><PERSON><PERSON><PERSON><PERSON>类\n", "#### 代码实现\n", "首次看代码一定是找到和旋转位置几个字最相关的类去看，那便是`modeling_qwen2.py`文件中的`Qwen2RotaryEmbedding`类，咱们首先看看这个类的代码："]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class Qwen2RotaryEmbedding(nn.Module):\n", "    \"\"\"Qwen2RotaryEmbedding类，基于modeling_qwen2.py中的Qwen2RotaryEmbedding类修改得到\n", "    \n", "    Args:\n", "        head_dim (int): 每个注意力头的维度\n", "        base (int, optional): 旋转位置编码中基频率，默认为10000\n", "        device (torch.device, optional): 设备，默认为None\n", "\n", "    \"\"\"\n", "\n", "    def __init__(self,\n", "                 head_dim: int,\n", "                 base: Optional[int] = 10000,\n", "                 device: Optional[torch.device] = None):\n", "        super().__init__()\n", "\n", "        # Compute the inverse frequencies\n", "        inv_freq = 1.0 / (base**(torch.arange(\n", "            0, head_dim, 2, dtype=torch.int64).float().to(device) / head_dim)\n", "                          )  # [head_dim//2]\n", "        self.register_buffer(\"inv_freq\", inv_freq, persistent=False)\n", "\n", "    @torch.no_grad()\n", "    def forward(self, x, position_ids):\n", "        # Core RoPE block\n", "        # x：[batch_size, num_attention_heads, seq_len, head_dim]\n", "        # position_ids：[batch_size, seq_len]\n", "        # inv_freq：[head_dim//2] -> inv_freq：[batch_size, head_dim//2, 1]\n", "        inv_freq_expanded = self.inv_freq[None, :, None].float().expand(\n", "            position_ids.shape[0], -1, 1)\n", "        # position_ids_expanded：[batch_size, 1, seq_len]\n", "        position_ids_expanded = position_ids[:, None, :].float()\n", "        # 确定张量所在的设备\n", "        device_type = x.device.type\n", "        device_type = device_type if isinstance(\n", "            device_type, str) and device_type != \"mps\" else \"cpu\"\n", "        \n", "        # autocast自动选择合适的精度\n", "        with torch.autocast(device_type=device_type, enabled=False):\n", "            # inv_freq_expanded：[batch_size, head_dim//2, 1]\n", "            # position_ids_expanded：[batch_size, 1, seq_len]\n", "            # freqs：[batch_size, seq_len, head_dim//2] -> 等价于inv_freq和position_ids的外积\n", "            freqs = (inv_freq_expanded.float()\n", "                     @ position_ids_expanded.float()).transpose(1, 2)\n", "            # emb：[batch_size, seq_len, head_dim] 就是角度频率\n", "            emb = torch.cat((freqs, freqs), dim=-1)\n", "            # cos: [batch_size, seq_len, head_dim]\n", "            # sin: [batch_size, seq_len, head_dim]\n", "            cos = emb.cos()\n", "            sin = emb.sin()\n", "\n", "        return cos.to(dtype=x.dtype), sin.to(dtype=x.dtype)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 关键代码分析\n", "\n", "搞懂上面这段代码的核心还是弄清楚维度的变化，可以看到`Qwen2RotaryEmbedding`类的`forward`函数的输出就是一个`cos`一个`sin`张量，维度是`[batch_size, seq_len, head_dim]`，说明**每个样本、每个token、每个特征维度**都有自己专属的`cos`和`sin`值，咱们先不管这个`cos`和`sin`值具体用在哪，先分析它如何获得。\n", "\n", "##### (1) inv_freq的计算\n", "\n", "```python\n", "inv_freq = 1.0 / (base**(torch.arange(\n", "            0, head_dim, 2, dtype=torch.int64).float().to(device) / head_dim))  # [head_dim//2]\n", "```\n", "\n", "其中，`base`是旋转位置编码的基频率，`head_dim`是每个注意力头的维度，`device`是设备。由于`head_dim`是每个注意力头的维度，可以判断**每个注意力头进行的位置编码操作**是一样的。上面这段代码实现的数学公式为：\n", "\n", "$$\n", "\\text{inv\\_freq}(i) = \\frac{1}{base^{\\frac{2i}{head\\_dim}}}\n", "$$\n", "\n", "其中，`i`是特征维度，`head_dim`是每个注意力头的维度。可以发现几个关键特点：\n", "1. 上述计算过程是针对每个特征维度`i`进行的，因此每个（几个）特征维度`i`都有自己专属的`inv_freq`值，`inv_freq`是弧度制角度，这个角度肯定和计算`cos`和`sin`值有关，进而可能跟旋转有关。\n", "2. `inv_freq`的维度是`[head_dim//2]`，即`head_dim`的一半，说明**极有可能是两个特征一组做一些操作**。\n", "\n", "##### 一个问题\n", "这里咱们先跳出旋转位置编码，有一个本人一直存在的问题：在位置编码当中，主要是确定不同token之间的相对位置，那为什么每个（或几个）特征都要有一个`inv_freq`旋转角度，**为啥不能所有特征共享一个角度，仅在不同位置的token之间存在角度区分**？\n", "\n", "**答**：是为了让不同的特征对位置的敏感程度保持不同。考虑绝对位置编码，就是对每个特征加上一个位置编码向量。假设存在一个token序列，序列长度为3，特征维度为4。现在我们进一步简化问题，我们仅考虑位置编码的角度，假设每个特征加的编码角度一样，也即角度仅和token位置有关，也即：\n", "$$\n", "\\mathbf{\\theta} = \\begin{bmatrix}\n", "10^\\circ & 10^\\circ & 10^\\circ & 10^\\circ \\\\\n", "20^\\circ & 20^\\circ & 20^\\circ & 20^\\circ \\\\\n", "30^\\circ & 30^\\circ & 30^\\circ & 30^\\circ\n", "\\end{bmatrix}\n", "$$\n", "这种模式虽然能够区别不同位置的token，但是每个位置所有特征差的角度值一样，导致每个特征对位置的敏感程度一样，**无法区分不同特征对位置的敏感程度**。具体而言，这种模式相邻位置每个token都差$10^\\circ$，所有特征都是等价敏感的。咱们换种模式，**让每个特征在不同位置的角度值不一样，且每个特征随着位置不同的角度变化程度也不同**，也即：\n", "$$\n", "\\mathbf{\\theta} = \\begin{bmatrix}\n", "10^\\circ & 20^\\circ & 30^\\circ & 40^\\circ \\\\\n", "20^\\circ & 40^\\circ & 60^\\circ & 80^\\circ \\\\\n", "30^\\circ & 60^\\circ & 90^\\circ & 120^\\circ\n", "\\end{bmatrix}\n", "$$\n", "可以发现，第一个特征每隔一个位置差$10^\\circ$，第二个特征每隔一个位置差$20^\\circ$，第三个特征每隔一个位置差$30^\\circ$，第四个特征每隔一个位置差$40^\\circ$，**这表明第一个特征对位置最不敏感，第四个特征对位置最敏感**。通过这种方式，token特征具备对距离的多尺度感知能力，能够同时学习出与位置相关和不相关的特征。现在，我们再来看大家熟悉的绝对位置编码的公式：\n", "$$\n", "PE(\\text{pos},2i) = \\sin\\left(\\frac{\\text{pos}}{10000^{2i/d_{model}}}\\right)\n", "$$\n", "$$\n", "PE(\\text{pos},2i+1) = \\cos\\left(\\frac{\\text{pos}}{10000^{2i/d_{model}}}\\right)\n", "$$\n", "其中，$d_{model}$是模型的特征维度，pos表示token在序列中的位置，$i$表示特征的第$i$个维度。可以发现，每个特征在不同位置的角度值不一样，且每个特征随着位置变化的角度变化程度也不同，**这表明每个特征对位置的敏感程度不同**。\n", "\n", "##### (2) cos和sin的计算\n", "\n", "```python\n", "# autocast自动选择合适的精度\n", "with torch.autocast(device_type=device_type, enabled=False):\n", "    # inv_freq_expanded：[batch_size, head_dim//2, 1]\n", "    # position_ids_expanded：[batch_size, 1, seq_len]\n", "    # freqs：[batch_size, seq_len, head_dim//2] -> 等价于inv_freq和position_ids的外积\n", "    freqs = (inv_freq_expanded.float()\n", "                @ position_ids_expanded.float()).transpose(1, 2)\n", "    # emb：[batch_size, seq_len, head_dim] 就是角度频率\n", "    emb = torch.cat((freqs, freqs), dim=-1)\n", "    # cos: [batch_size, seq_len, head_dim]\n", "    # sin: [batch_size, seq_len, head_dim]\n", "    cos = emb.cos()\n", "    sin = emb.sin() \n", "```\n", "\n", "1. `position_ids_expanded`的维度是`[batch_size, 1, seq_len]`，`inv_freq_expanded`的维度是`[batch_size, head_dim//2, 1]`，这么搞是为了进行矩阵乘法，得到`freqs`矩阵。需要注意的是。对于每个样本的`position_ids`，就是token在序列当中的位置，就是一个从0开始的整型数组，比如`[0, 1, 2 ,3]`。\n", "2. `freqs`的维度是`[batch_size, seq_len, head_dim//2]`：对于每一个样本，行数表示第几个token，列数表示第几个特征。\n", "3. `emb`的维度是`[batch_size, seq_len, head_dim]`：对于每一个样本，行数表示第几个token，列数表示第几个特征，是角度值。这里是对`freqs`矩阵进行拼接操作，对于`head_dim`维，**前一半和后一半元素是重复的**。\n", "4. `cos`和`sin`就是`emb`对应的正弦和余弦值，维度是`[batch_size, seq_len, head_dim]`：对于每一个样本，行数表示第几个token，列数表示第几个特征。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 测试一下"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[ 1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000],\n", "        [ 0.5403,  0.9950,  0.9999,  1.0000,  0.5403,  0.9950,  0.9999,  1.0000],\n", "        [-0.4161,  0.9801,  0.9998,  1.0000, -0.4161,  0.9801,  0.9998,  1.0000],\n", "        [-0.9900,  0.9553,  0.9996,  1.0000, -0.9900,  0.9553,  0.9996,  1.0000]],\n", "       device='cuda:0')\n", "tensor([[ 1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000],\n", "        [ 0.5403,  0.9950,  0.9999,  1.0000,  0.5403,  0.9950,  0.9999,  1.0000],\n", "        [-0.4161,  0.9801,  0.9998,  1.0000, -0.4161,  0.9801,  0.9998,  1.0000],\n", "        [-0.9900,  0.9553,  0.9996,  1.0000, -0.9900,  0.9553,  0.9996,  1.0000]],\n", "       device='cuda:0')\n", "tensor([[0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.8415, 0.0998, 0.0100, 0.0010, 0.8415, 0.0998, 0.0100, 0.0010],\n", "        [0.9093, 0.1987, 0.0200, 0.0020, 0.9093, 0.1987, 0.0200, 0.0020],\n", "        [0.1411, 0.2955, 0.0300, 0.0030, 0.1411, 0.2955, 0.0300, 0.0030]],\n", "       device='cuda:0')\n", "torch.<PERSON><PERSON>([2, 4, 8])\n", "torch.<PERSON><PERSON>([2, 4, 8])\n", "tensor([1.0000, 0.1000, 0.0100, 0.0010], device='cuda:0')\n", "torch.<PERSON><PERSON>([2, 4, 8])\n", "tensor([[0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00],\n", "        [1.0000e+00, 1.0000e-01, 1.0000e-02, 1.0000e-03, 1.0000e+00, 1.0000e-01,\n", "         1.0000e-02, 1.0000e-03],\n", "        [2.0000e+00, 2.0000e-01, 2.0000e-02, 2.0000e-03, 2.0000e+00, 2.0000e-01,\n", "         2.0000e-02, 2.0000e-03],\n", "        [3.0000e+00, 3.0000e-01, 3.0000e-02, 3.0000e-03, 3.0000e+00, 3.0000e-01,\n", "         3.0000e-02, 3.0000e-03]], device='cuda:0')\n"]}], "source": ["# 所需变量声明\n", "head_dim = 8\n", "base = 10000\n", "batch_size = 2\n", "seq_len = 4\n", "position_ids = torch.arange(seq_len, device=torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")).repeat(batch_size, 1)   # [batch_size, seq_len]\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "x = torch.randn(batch_size, seq_len, head_dim, device=device) # [batch_size, seq_len, head_dim]\n", "\n", "# 初始化Qwen2RotaryEmbedding类\n", "rotary_embedding = Qwen2RotaryEmbedding(head_dim, base, device)\n", "# cos: [batch_size, seq_len, head_dim]\n", "# sin: [batch_size, seq_len, head_dim]\n", "cos, sin = rotary_embedding(x, position_ids)\n", "\n", "# 测试一：打印不同batch样本的cos值\n", "print(cos[0])\n", "print(cos[1])\n", "# 结论：cos和sin矩阵的后四列是前四列的复刻，每个batch所有的cos和sin的值相等\n", "\n", "# 测试二：打印一下sin值，看看是否和cos值对应\n", "print(sin[0])\n", "# 结论：sin值和cos值对应\n", "\n", "# 测试三：打印cos和sin的shape\n", "print(cos.shape)\n", "print(sin.shape)\n", "# 结论：[batch_size, seq_len, head_dim]\n", "\n", "# 测试四：看一下inv_freq是啥\n", "'''\n", "inv_freq = 1.0 / (base**(torch.arange(\n", "            0, head_dim, 2, dtype=torch.int64).float().to(device) / head_dim)\n", "                          )\n", "'''\n", "# 维度：[head_dim//2]，可以发现特征之间的inv_freq不同\n", "print(rotary_embedding.inv_freq) \n", "# 结论：inv_freq是一个递减的值\n", "\n", "# 测试五：看一下emb是什么，也即角度是什么，很关键！\n", "inv_freq_expanded = rotary_embedding.inv_freq[None, :, None].float().expand(\n", "            position_ids.shape[0], -1, 1)\n", "position_ids_expanded = position_ids[:, None, :].float()\n", "freqs = (inv_freq_expanded.float()\n", "                @ position_ids_expanded.float()).transpose(1, 2)\n", "emb = torch.cat((freqs, freqs), dim=-1)\n", "print(emb.shape)\n", "# 看一个batch，每个样本都是一样的\n", "# 保留4位小数打印\n", "print(emb[0])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 重要信息\n", "\n", "通过上面的测试，分析`emb`能够得到重要信息，旋转位置编码对于`[seq_len, dim]`的token特征，每个token位置`pos`，每个特征维度`i`都有一个角度值。令$\\text{inv\\_freq}(i)=\\theta_{i}$：\n", "$$\n", "\\text{emb}(\\text{pos}, i) = \\begin{cases}\n", "\\text{pos} \\times \\theta_{i} &  i < \\frac{\\text{head\\_dim}}{2} \\\\\n", "\\text{pos} \\times \\theta_{i - \\frac{\\text{head\\_dim}}{2}} & i \\geq \\frac{\\text{head\\_dim}}{2}\n", "\\end{cases}\n", "$$\n", "这个结论很重要，每个特征有一个基础角度值$\\theta_{i}$，然后这个角度会随着token位置的变化而变化，就是简单的乘上token在序列中的位置，从0开始。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Qwen2Attention类\n", "通过进一步寻找旋转位置编码的出现踪迹，能够发现它是运用在了注意力机制的计算过程当中，咱们锁定`modeling_qwen2.py`文件中的`Qwen2Attention`类，并查看它的代码："]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["class Qwen2Attention(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    Multi-headed attention from 'Attention Is All You Need' paper\n", "    基于Qwen2.5Attention类简化修改\n", "    \"\"\"\n", "    \n", "    \"\"\"Qwen2Attention\n", "    \n", "    Args:\n", "        head_dim (int): 每个注意力头维度\n", "        num_attention_heads (int): 注意力头数量\n", "        attention_dropout (float, optional): 注意力dropout概率\n", "    \"\"\"\n", "\n", "    def __init__(self, \n", "                 head_dim: int,\n", "                 num_attention_heads: int,\n", "                 attention_dropout: float = 0.0,\n", "                 ):\n", "        super().__init__()\n", "        \n", "        # 每个注意力头的特征维度\n", "        self.head_dim = head_dim  \n", "        # hidden_size就是总特征数目=每个头的特征维度*注意力头的个数\n", "        self.hidden_size = head_dim * num_attention_heads\n", "        self.attention_dropout = attention_dropout\n", "        \n", "        # Q、K、V、O的线性层\n", "        self.q_proj = nn.Linear(self.hidden_size, num_attention_heads * self.head_dim, bias=True)\n", "        self.k_proj = nn.Linear(self.hidden_size, num_attention_heads * self.head_dim, bias=True)\n", "        self.v_proj = nn.Linear(self.hidden_size, num_attention_heads * self.head_dim, bias=True)\n", "        self.o_proj = nn.Linear(num_attention_heads * self.head_dim, self.hidden_size, bias=False)\n", "    \n", "    def apply_rotary_pos_emb(self, q, k, cos, sin, unsqueeze_dim=1):\n", "        \"\"\"Applies Rotary Position Embedding to the query and key tensors.\n", "        该函数来自Modeling_qwen2.py，进行了修改简化\n", "  \n", "        Args:\n", "            q (`torch.Tensor`): The query tensor. [batch_size, num_attention_heads, seq_len, head_dim]\n", "            k (`torch.Tensor`): The key tensor. [batch_size, num_attention_heads, seq_len, head_dim]\n", "            cos (`torch.Tensor`): The cosine part of the rotary embedding.\n", "            sin (`torch.Tensor`): The sine part of the rotary embedding.\n", "            unsqueeze_dim (`int`, *optional*, defaults to 1):\n", "                The 'unsqueeze_dim' argument specifies the dimension along which to unsqueeze cos[position_ids] and\n", "                sin[position_ids] so that they can be properly broadcasted to the dimensions of q and k. For example, note\n", "                that cos[position_ids] and sin[position_ids] have the shape [batch_size, seq_len, head_dim]. Then, if q and\n", "                k have the shape [batch_size, heads, seq_len, head_dim], then setting unsqueeze_dim=1 makes\n", "                cos[position_ids] and sin[position_ids] broadcastable to the shapes of q and k. Similarly, if q and k have\n", "                the shape [batch_size, seq_len, heads, head_dim], then set unsqueeze_dim=2.\n", "        Returns:\n", "            `tuple(torch.Tensor)` comprising of the query and key tensors rotated using the Rotary Position Embedding.\n", "        \"\"\"\n", "        cos = cos.unsqueeze(unsqueeze_dim)\n", "        sin = sin.unsqueeze(unsqueeze_dim)\n", "        q_embed = (q * cos) + (self.rotate_half(q) * sin)\n", "        k_embed = (k * cos) + (self.rotate_half(k) * sin)\n", "\n", "        return q_embed, k_embed\n", "\n", "    def rotate_half(self, x):\n", "        \"\"\"Rotates half the hidden dims of the input.\"\"\"\n", "        x1 = x[..., : x.shape[-1] // 2]\n", "        x2 = x[..., x.shape[-1] // 2 :]\n", "        return torch.cat((-x2, x1), dim=-1)\n", "    \n", "    def attention_forward(\n", "        self,\n", "        module: nn.<PERSON><PERSON>,\n", "        query: torch.<PERSON><PERSON>,\n", "        key: <PERSON>.<PERSON><PERSON>,\n", "        value: torch.Ten<PERSON>,\n", "        scaling: float,\n", "        attention_mask: Optional[torch.Tensor] = None,\n", "        dropout: Optional[float] = 0.0,\n", "):\n", "        \"\"\"attention_forward：修改自Modeling_qwen2.py中的eager_attention_forward函数\n", "\n", "        Args:\n", "            module (nn.<PERSON><PERSON><PERSON>): PyTorch用于计算注意力机制的类\n", "            query (torch.Tensor): 查询张量\n", "            key (torch.Tensor): 键张量\n", "            value (torch.Tensor): 值张量\n", "            scaling (float): 缩放因子\n", "            attention_mask (Optional[torch.Tensor]): 注意力掩码\n", "            dropout (float, optional): 注意力dropout概率\n", "\n", "        Returns:\n", "            Tuple[torch.Tensor, torch.Tensor]: 注意力机制计算完的输出与注意力权重\n", "        \"\"\"\n", "        key_states = key  # [batch_size, num_attention_heads, seq_len, head_dim]\n", "        value_states = value  # [batch_size, num_attention_heads, seq_len, head_dim]\n", "\n", "        attn_weights = torch.matmul(query, key_states.transpose(2, 3)) * scaling # [batch_size, num_attention_heads, seq_len, seq_len]\n", "        \n", "        # 暂不考虑mask\n", "        if attention_mask is not None:\n", "            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]\n", "            attn_weights = attn_weights + causal_mask\n", "\n", "        # 计算注意力权重\n", "        attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query.dtype)\n", "        # 计算注意力权重dropout\n", "        attn_weights = nn.functional.dropout(attn_weights, p=dropout, training=module.training)\n", "        \n", "        # 计算注意力输出\n", "        attn_output = torch.matmul(attn_weights, value_states) # [batch_size, num_attention_heads, seq_len, head_dim]\n", "        attn_output = attn_output.transpose(1, 2).contiguous() # [batch_size, seq_len, num_attention_heads, head_dim]\n", "        \n", "        return attn_output, attn_weights\n", "\n", "    def forward(\n", "        self,\n", "        hidden_states: torch.<PERSON><PERSON>,\n", "        position_embeddings: <PERSON><PERSON>[torch.Tensor, torch.Tensor],\n", "        attention_mask: Optional[torch.Tensor],\n", "    ) -> <PERSON><PERSON>[torch.Tensor, Optional[torch.Tensor], Optional[Tu<PERSON>[torch.Tensor]]]:\n", "        # hidden_states: [batch_size, seq_len, hidden_size]\n", "        # position_embeddings: [batch_size, seq_len, head_dim]\n", "        # attention_mask: [batch_size, seq_len]  随机生成，并不主要考虑\n", "        \n", "        input_shape = hidden_states.shape[:-1]  # [batch_size, seq_len]\n", "        hidden_shape = (*input_shape, -1, self.head_dim) # [batch_size, seq_len, num_attention_heads, head_dim]  \n", "\n", "        query_states = self.q_proj(hidden_states).view(hidden_shape).transpose(1, 2) # [batch_size, num_attention_heads, seq_len, head_dim]\n", "        key_states = self.k_proj(hidden_states).view(hidden_shape).transpose(1, 2) # [batch_size, num_attention_heads, seq_len, head_dim]\n", "        value_states = self.v_proj(hidden_states).view(hidden_shape).transpose(1, 2) # [batch_size, num_attention_heads, seq_len, head_dim]\n", "\n", "        # 关键！来自于Qwen2RotaryEmbedding类forward函数的输出\n", "        cos, sin = position_embeddings\n", "        # 关键！将旋转位置编码添加到了query和key上\n", "        query_states, key_states = self.apply_rotary_pos_emb(query_states, key_states, cos, sin)\n", "\n", "    \n", "        # 关键！用添加过旋转位置编码的特征“照常”计算注意力机制\n", "        attention_interface: Callable = self.attention_forward\n", "        attn_output, attn_weights = attention_interface(\n", "            self,\n", "            query_states,\n", "            key_states,\n", "            value_states,\n", "            attention_mask,\n", "            dropout=0.0 if not self.training else self.attention_dropout,\n", "            scaling=self.scaling,\n", "        )\n", "\n", "        attn_output = attn_output.reshape(*input_shape, -1).contiguous()\n", "        attn_output = self.o_proj(attn_output)\n", "        \n", "        return attn_output, attn_weights\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 关键代码分析\n", "##### （1）rotate_half函数\n", "\n", "```python\n", "def rotate_half(self, x):\n", "    \"\"\"Rotates half the hidden dims of the input.\"\"\"\n", "    x1 = x[..., : x.shape[-1] // 2]\n", "    x2 = x[..., x.shape[-1] // 2 :]\n", "    return torch.cat((-x2, x1), dim=-1)\n", "```\n", "\n", "通过代码能够发现，之前计算获得的`cos`和`sin`矩阵用在了对`query`和`key`的“改造”上。首先就是使用了`rotate_half`函数对输入的张量x进行处理，具体而言先把后面一半特征取负号以后拿到前面，具体代码如下：   "]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 测试一下"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始x:\n", "tensor([ 0.5804, -0.4294,  0.1393,  0.3164,  0.3950, -0.0735])\n", "\n", "旋转后x:\n", "tensor([-0.3164, -0.3950,  0.0735,  0.5804, -0.4294,  0.1393])\n"]}], "source": ["# 参数声明\n", "batch_size=2\n", "head_dim = 6\n", "num_attention_heads = 2\n", "seq_len = 4\n", "# 类别定义\n", "attention = Qwen2Attention(head_dim=head_dim,\n", "                           num_attention_heads=num_attention_heads)\n", "# 测试rotate_half函数\n", "\n", "# 1. 给一个x\n", "x = torch.randn((batch_size, num_attention_heads, seq_len, head_dim))\n", "\n", "# 2. 调用rotate_half函数\n", "rotated_x = attention.rotate_half(x)        \n", "\n", "# 查看具体数值（给定样本，给定注意力头，给定token位置）\n", "print(\"原始x:\")\n", "print(x[0,0,0,:])\n", "print(\"\\n旋转后x:\") \n", "print(rotated_x[0,0,0,:])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### (2) apply_rotary_pos_emb函数\n", "\n", "```python\n", "def apply_rotary_pos_emb(self, q, k, cos, sin, unsqueeze_dim=1):\n", "    \"\"\"Applies Rotary Position Embedding to the query and key tensors.\n", "    该函数来自Modeling_qwen2.py，进行了修改简化\n", "\n", "    Args:\n", "        q (`torch.Tensor`): The query tensor. \n", "        [batch_size, num_attention_heads, seq_len, head_dim]\n", "        k (`torch.Tensor`): The key tensor. \n", "        [batch_size, num_attention_heads, seq_len, head_dim]\n", "        cos (`torch.Tensor`): The cosine part of the rotary embedding. \n", "        [batch_size, seq_len, head_dim]\n", "        sin (`torch.Tensor`): The sine part of the rotary embedding. \n", "        [batch_size, seq_len, head_dim]\n", "        unsqueeze_dim (`int`, *optional*, defaults to 1): The 'unsqueeze_dim' argument specifies the dimension along which to unsqueeze cos[position_ids] and\n", "    Returns:\n", "        `tuple(torch.Tensor)` comprising of the query and key tensors rotated using the Rotary Position Embedding.\n", "    \"\"\"\n", "    cos = cos.unsqueeze(unsqueeze_dim)\n", "    sin = sin.unsqueeze(unsqueeze_dim)\n", "    q_embed = (q * cos) + (self.rotate_half(q) * sin)\n", "    k_embed = (k * cos) + (self.rotate_half(k) * sin)\n", "\n", "    return q_embed, k_embed\n", "```\n", "\n", "`apply_rotary_pos_emb`函数的作用和它的名字一样，就是应用旋转位置编码。与传统的绝对位置编码不同，旋转位置编码的“添加”对象不是原始的token特征，而是query和key的特征（也即多经过了一个线性层）。这里对原始的query和key进行了一些操作，直接上公式还是太抽象了，咱们举一个例子来看："]}, {"cell_type": "markdown", "metadata": {}, "source": ["同样，由于每个注意力头和每个样本的计算过程是一样的，为了简化操作，我们现只考虑一个位置的token都做了些什么。假设第$m$个token对应的query向量为$\\mathbf{q}$，对应的key向量为$\\mathbf{k}$，特征维度为4，有:\n", "$$\n", "\\mathbf{q} = \\begin{bmatrix}\n", "q_{1} & q_{2} & q_{3} & q_{4}\n", "\\end{bmatrix}\n", "$$\n", "$$\n", "\\mathbf{k} = \\begin{bmatrix}\n", "k_{1} & k_{2} & k_{3} & k_{4}\n", "\\end{bmatrix}\n", "$$\n", "又假设之前获得的对应token的cos和sin向量为：\n", "$$\n", "\\cos = \\begin{bmatrix}\n", "\\cos m\\theta_{1} & \\cos m\\theta_{2} & \\cos m\\theta_{1} & \\cos m\\theta_{2}\n", "\\end{bmatrix}\n", "$$\n", "$$\n", "\\sin = \\begin{bmatrix}\n", "\\sin m\\theta_{1} & \\sin m\\theta_{2} & \\sin m\\theta_{1} & \\sin m\\theta_{2}\n", "\\end{bmatrix}\n", "$$\n", "\n", "按照上述代码的描述，$\\mathbf{q}_\\text{emb}$和$\\mathbf{k}_\\text{emb}$向量的计算过程如下：\n", "$$\n", "\\mathbf{q}_\\text{emb} = \\mathbf{q} * \\cos + \\text{rotate\\_half}(\\mathbf{q}) * \\sin\n", "$$\n", "$$\n", "\\mathbf{k}_\\text{emb} = \\mathbf{k} * \\cos + \\text{rotate\\_half}(\\mathbf{k}) * \\sin\n", "$$\n", "其中*表示逐元素计算。由于query和key向量的计算过程一样，我们仅分析query向量的具体过程：\n", "$$\n", "\\begin{aligned}\n", "    \\mathbf{q}_\\text{emb} &= \\begin{bmatrix}\n", "    q_{1}\\cos m\\theta_{1} & q_{2}\\cos m\\theta_{2} & q_{3}\\cos m\\theta_{1} & q_{4}\\cos m\\theta_{2} \n", "    \\end{bmatrix} +  \\\\\n", "    & \\begin{bmatrix}\n", "    -q_{3}\\sin m\\theta_{1} & -q_{4}\\sin m\\theta_{2} & q_{1}\\sin m\\theta_{1} & q_{2}\\sin m\\theta_{2}\n", "    \\end{bmatrix}  \\\\\n", "    &= \\begin{bmatrix}\n", "    q_{1}\\cos m\\theta_{1} - q_{3}\\sin m\\theta_{1} & q_{2}\\cos m\\theta_{2} - q_{4}\\sin m\\theta_{2} & q_{3}\\cos m\\theta_{1} + q_{1}\\sin m\\theta_{1} & q_{4}\\cos m\\theta_{2} + q_{2}\\sin m\\theta_{2}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "    q^{\\prime}_{1} & q^{\\prime}_{2} & q^{\\prime}_{3} & q^{\\prime}_{4}\n", "    \\end{bmatrix}\n", "\\end{aligned}\n", "$$\n", "其中，带“撇”的就是旋转后的特征。这里就能看出一些端倪，结合“旋转”二字，我们看第1个和第3个特征，有：\n", "$$\n", "\\begin{aligned}\n", "    \\begin{bmatrix}\n", "     q^{\\prime}_{1} \\\\\n", "     q^{\\prime}_{3}\n", "     \\end{bmatrix} &= \\begin{bmatrix}\n", "     \\cos m\\theta_{1} & -\\sin m\\theta_{1} \\\\\n", "     \\sin m\\theta_{1} & \\cos m\\theta_{1}\n", "     \\end{bmatrix} \\begin{bmatrix}\n", "     q_{1} \\\\\n", "     q_{3}\n", "     \\end{bmatrix} \\\\\n", "    &= \\mathbf{R}(m\\theta_{1}) \\begin{bmatrix}\n", "    q_{1} \\\\\n", "    q_{3}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "     q_{1}\\cos m\\theta_{1} - q_{3}\\sin m\\theta_{1} \\\\\n", "     q_{1}\\sin m\\theta_{1} + q_{3}\\cos m\\theta_{1}\n", "     \\end{bmatrix} \n", "\\end{aligned}\n", "$$\n", "\n", "其中，$\\mathbf{R}(m\\theta_{1})$是一个旋转矩阵，它的旋转角度为$m\\theta_{1}$，相当于把向量$[q_{1}, q_{3}]^T$旋转了$m\\theta_{1}$的角度。我们继续看第2个和第4个特征，有：\n", "$$\n", "\\begin{aligned}\n", "    \\begin{bmatrix}\n", "     q^{\\prime}_{2} \\\\\n", "     q^{\\prime}_{4}\n", "     \\end{bmatrix} &= \\begin{bmatrix}\n", "     \\cos m\\theta_{2} & -\\sin m\\theta_{2} \\\\\n", "     \\sin m\\theta_{2} & \\cos m\\theta_{2}\n", "     \\end{bmatrix} \\begin{bmatrix}\n", "     q_{2} \\\\\n", "     q_{4}\n", "     \\end{bmatrix} \\\\\n", "    &= \\mathbf{R}(m\\theta_{2}) \\begin{bmatrix}\n", "    q_{2} \\\\\n", "    q_{4}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "     q_{2}\\cos m\\theta_{2} - q_{4}\\sin m\\theta_{2} \\\\\n", "     q_{2}\\sin m\\theta_{2} + q_{4}\\cos m\\theta_{2}\n", "     \\end{bmatrix}\n", "\\end{aligned}\n", "$$\n", "同样，$\\mathbf{R}(m\\theta_{2})$也是一个旋转矩阵，它的旋转角度为$m\\theta_{2}$，相当于把向量$[q_{2}, q_{4}]^T$旋转了$m\\theta_{2}$的角度。这里，key的计算过程和query完全一样，就是把$q$换成$k$而已，不再赘述。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 重要结论\n", "\n", "对于每一个样本的每一个token而言，query和key的特征向量成分被**两两组合**，分别旋转不同的角度，这个角度由`inv_freq`计算得到基础角度$\\theta_{i}$，再根据不同序列位置倍增基础角度。至于为什么Qwen选择了这种前一半和后一半的组合方式，是因为计算效率高，当然你采用其他组合方式也完全可以，没有限制。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### （3）attention_forward函数\n", "\n", "```python   \n", "query_states, key_states = self.apply_rotary_pos_emb(query_states, key_states, cos, sin)\n", "# 关键！用添加过旋转位置编码的特征“照常”计算注意力机制\n", "attention_interface: Callable = self.attention_forward\n", "attn_output, attn_weights = attention_interface(\n", "    self,\n", "    query_states,\n", "    key_states,\n", "    value_states,\n", "    attention_mask,\n", "    dropout=0.0 if not self.training else self.attention_dropout,\n", "    scaling=self.scaling,\n", ")\n", "```\n", "可能有人会觉得奇怪，咋不分析`attention_forward`函数呢？其实这个函数就是普通的注意力机制计算过程，**唯一需要注意的事它输入的query和key是经过旋转位置编码处理过后的**！！！虽然我们知道了每个token到底因为旋转编码的操作做了什么改变，但我们还有最后一个也是最关键的问题需要处理，**就是不同位置的token之间相对位置是怎么伴随着旋转位置编码实现的**？代码的最后部分就是注意力机制计算，看来最后的问题也将随着注意力机制的计算过程的分析而迎刃而解。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来，我们就来模拟在旋转位置编码下的注意力机制计算过程，就能够搞懂这最后一个问题。我们还是不区分样本和注意力头，因为每个样本和注意力头的计算过程是一样的。假设我们有一个token序列，特征维度还是4，第$m$个token的query与第$n$个token的key的表示如下：\n", "$$\n", "\\begin{aligned}\n", "    \\mathbf{q}_m^{\\prime} &= \\begin{bmatrix}\n", "    q_{m1}^{\\prime} & q_{m2}^{\\prime} & q_{m3}^{\\prime} & q_{m4}^{\\prime}\n", "    \\end{bmatrix} \\\\\n", "    \\mathbf{k}_n^{\\prime} &= \\begin{bmatrix}\n", "    k_{n1}^{\\prime} & k_{n2}^{\\prime} & k_{n3}^{\\prime} & k_{n4}^{\\prime}\n", "    \\end{bmatrix}\n", "\\end{aligned}\n", "$$\n", "为了计算注意力，每两个token之间的query和key向量都要进行点积计算，这里我们仅考虑第$m$个token和第$n$个token的计算过程，有：\n", "$$\n", "\\begin{aligned}\n", "    \\text{score}_{mn} &= \\mathbf{q}_m^{\\prime} \\cdot \\mathbf{k}_n^{\\prime} \\\\\n", "    &= \\begin{bmatrix}\n", "    q_{m1}^{\\prime} & q_{m2}^{\\prime} & q_{m3}^{\\prime} & q_{m4}^{\\prime}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    k_{n1}^{\\prime} \\\\\n", "    k_{n2}^{\\prime} \\\\\n", "    k_{n3}^{\\prime} \\\\\n", "    k_{n4}^{\\prime}\n", "    \\end{bmatrix} \\\\\n", "    &= q_{m1}^{\\prime}k_{n1}^{\\prime} + q_{m2}^{\\prime}k_{n2}^{\\prime} + q_{m3}^{\\prime}k_{n3}^{\\prime} + q_{m4}^{\\prime}k_{n4}^{\\prime} \\\\\n", "    &= (q_{m1}^{\\prime}k_{n1}^{\\prime} + q_{m3}^{\\prime}k_{n3}^{\\prime}) + (q_{m2}^{\\prime}k_{n2}^{\\prime} + q_{m4}^{\\prime}k_{n4}^{\\prime}) \n", "\\end{aligned}\n", "$$\n", "乍看之下，这和相对位置有啥关系？别急，是不是看到我按组整理了一下，让我们按组代入旋转矩阵试一试：\n", "$$\n", "\\begin{aligned}\n", "    q_{m1}^{\\prime}k_{n1}^{\\prime} + q_{m3}^{\\prime}k_{n3}^{\\prime} &= \n", "    \\begin{bmatrix}\n", "    q_{m1}^{\\prime} & q_{m3}^{\\prime}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    k_{n1}^{\\prime} \\\\\n", "    k_{n3}^{\\prime}\n", "    \\end{bmatrix} \\\\\n", "    &= \\left[\\mathbf{R}(m\\theta_{1}) \\begin{bmatrix}\n", "    q_{m1} \\\\ \n", "    q_{m3}\n", "    \\end{bmatrix}\\right]^T   \\mathbf{R}(n\\theta_{1})  \\begin{bmatrix}\n", "    k_{n1} \\\\\n", "    k_{n3}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "    q_{m1} & q_{m3}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    \\cos n\\theta_{1} & \\sin n\\theta_{1} \\\\\n", "    -\\sin n\\theta_{1} & \\cos n\\theta_{1} \n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    \\cos m\\theta_{1} & -\\sin m\\theta_{1} \\\\\n", "    \\sin m\\theta_{1} & \\cos m\\theta_{1}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    k_{n1} \\\\\n", "    k_{n3}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "    q_{m1} & q_{n3}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    \\cos m\\theta_{1}\\cos n\\theta_{1} + \\sin m\\theta_{1}\\sin n\\theta_{1} & \\cos m\\theta_{1}\\sin n\\theta_{1} - \\sin m\\theta_{1}\\cos n\\theta_{1} \\\\\n", "    \\sin m\\theta_{1}\\cos n\\theta_{1} - \\cos m\\theta_{1}\\sin n\\theta_{1} & \\sin m\\theta_{1}\\sin n\\theta_{1} + \\cos m\\theta_{1}\\cos n\\theta_{1}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    k_{n1} \\\\\n", "    k_{n3}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "    q_{m1} & q_{m3}\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    \\cos (m\\theta_{1} - n\\theta_{1}) & -\\sin (m\\theta_{1} - n\\theta_{1}) \\\\\n", "    \\sin (m\\theta_{1} - n\\theta_{1}) & \\cos (m\\theta_{1} - n\\theta_{1})\n", "    \\end{bmatrix} \\begin{bmatrix}\n", "    k_{n1} \\\\\n", "    k_{n3}\n", "    \\end{bmatrix} \\\\\n", "    &= \\begin{bmatrix}\n", "    q_{m1} & q_{m3}\n", "    \\end{bmatrix} \\mathbf{R}((m-n)\\theta_{1}) \\begin{bmatrix}\n", "    k_{n1} \\\\\n", "    k_{n3}\n", "    \\end{bmatrix}\n", "\\end{aligned}\n", "$$\n", "\n", "同理，有：\n", "$$\n", "\\begin{aligned}\n", "    q_{m2}^{\\prime}k_{n2}^{\\prime} + q_{m4}^{\\prime}k_{n4}^{\\prime} &= \\begin{bmatrix}\n", "    q_{m2} & q_{m4}\n", "    \\end{bmatrix} \\mathbf{R}((m-n)\\theta_{2}) \\begin{bmatrix}\n", "    k_{n2} \\\\\n", "    k_{n4}\n", "    \\end{bmatrix}\n", "\\end{aligned}\n", "$$\n", "\n", "通过公式能够看出，旋转位置编码的核心就是在计算注意力的时候，**每个token的query和key向量成分都被两两组合，分别旋转不同的角度**。当两个不同位置token的query和key进行点乘计算时候，点乘的结果仅受到两个token之间旋转角度差值的影响，而旋转角度的差值又仅和两个token的相对位置有关，这样就能够实现**不同位置token之间的相对位置信息的表示**。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 总结\n", "\n", "至此，我们以终为始的完成了旋转位置编码知识点的梳理。个人来看，旋转位置编码的代码实现比其数学理论要简单很多，通过代码入手的难度反而较低更利于理解。旋转位置编码主要拥有以下优点或特点：\n", "1. RoPE的旋转机制不依赖预设的最大序列长度，理论上可处理任意长度的输入。相比传统位置编码，RoPE在超长文本任务中表现更稳定。\n", "2. 在计算注意力机制前改变query和key的值，不改变现有的注意力机制计算过程，代码改动小。"]}], "metadata": {"kernelspec": {"display_name": "openr1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}