version: '3.8'

services:
  # 智能搜索API服务
  search-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: intelligent-search-api
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - API_HOST=0.0.0.0
      - API_PORT=8000
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - search-network

  # Gradio前端服务
  search-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: intelligent-search-frontend
    ports:
      - "7860:7860"
    environment:
      - API_BASE_URL=http://search-api:8000
      - FRONTEND_HOST=0.0.0.0
      - FRONTEND_PORT=7860
    depends_on:
      search-api:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - search-network

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: intelligent-search-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - search-network
    profiles:
      - with-cache

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: intelligent-search-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - search-api
      - search-frontend
    restart: unless-stopped
    networks:
      - search-network
    profiles:
      - with-proxy

volumes:
  redis_data:
    driver: local

networks:
  search-network:
    driver: bridge
