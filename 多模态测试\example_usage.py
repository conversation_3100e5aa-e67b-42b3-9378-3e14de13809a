#!/usr/bin/env python3
"""
使用示例：展示如何使用DocumentProcessor处理不同类型的文档
"""

import os
from document_processor import DocumentProcessor

def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 初始化处理器
    processor = DocumentProcessor()
    
    # 处理PDF文档
    result = processor.process_document(
        file_path="sample.pdf",
        output_dir="./output/basic",
        analyze_images=True
    )
    
    print(f"处理完成: {result['enhanced_markdown']}")

def example_custom_prompt():
    """自定义图片分析提示词示例"""
    print("\n=== 自定义提示词示例 ===")
    
    processor = DocumentProcessor()
    
    # 针对技术文档的图片分析
    tech_prompt = """
    请分析这张图片，重点关注：
    1. 技术架构图或流程图的结构
    2. 代码片段或配置信息
    3. 数据表格或统计图表
    4. 关键的技术概念或术语
    用中文详细描述，适合技术人员理解。
    """
    
    result = processor.process_document(
        file_path="technical_doc.pdf",
        output_dir="./output/technical",
        analyze_images=True,
        image_analysis_prompt=tech_prompt
    )

def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    processor = DocumentProcessor()
    
    # 批量处理多个文档
    documents = [
        "doc1.pdf",
        "doc2.docx", 
        "doc3.pdf"
    ]
    
    results = []
    for doc in documents:
        if os.path.exists(doc):
            print(f"\n处理文档: {doc}")
            result = processor.process_document(
                file_path=doc,
                output_dir=f"./output/batch/{os.path.splitext(doc)[0]}",
                analyze_images=True
            )
            results.append(result)
    
    print(f"\n批量处理完成，共处理 {len(results)} 个文档")

def example_no_image_analysis():
    """不分析图片的示例（仅提取和保存图片）"""
    print("\n=== 仅提取图片示例 ===")
    
    processor = DocumentProcessor()
    
    result = processor.process_document(
        file_path="large_doc.pdf",
        output_dir="./output/extract_only",
        analyze_images=False  # 不分析图片，节省时间和成本
    )
    
    print("仅提取完成，图片已保存但未分析")

def example_different_models():
    """使用不同模型的示例"""
    print("\n=== 不同模型示例 ===")
    
    # 注意：这需要修改DocumentProcessor类来支持模型选择
    # 这里只是展示概念
    
    prompts = {
        "简洁模式": "用一句话简洁描述这张图片的主要内容。",
        "详细模式": "请详细描述这张图片的所有内容，包括文字、图表、颜色、布局等。",
        "专业模式": "从专业角度分析这张图片，重点关注技术细节、数据信息、业务逻辑等。"
    }
    
    processor = DocumentProcessor()
    
    for mode, prompt in prompts.items():
        print(f"\n使用{mode}处理...")
        result = processor.process_document(
            file_path="test_doc.pdf",
            output_dir=f"./output/{mode.replace('模式', '')}",
            analyze_images=True,
            image_analysis_prompt=prompt
        )

def example_result_analysis():
    """结果分析示例"""
    print("\n=== 结果分析示例 ===")
    
    processor = DocumentProcessor()
    
    result = processor.process_document(
        file_path="analysis_doc.pdf",
        output_dir="./output/analysis",
        analyze_images=True
    )
    
    # 分析处理结果
    stats = result['stats']
    print(f"\n📊 处理统计:")
    print(f"- 总图片数: {stats['total_images']}")
    print(f"- 已分析图片: {stats['analyzed_images']}")
    print(f"- Markdown长度: {stats['markdown_length']} 字符")
    print(f"- 分析成功率: {stats['analyzed_images']/stats['total_images']*100:.1f}%")
    
    # 读取增强后的Markdown
    with open(result['enhanced_markdown'], 'r', encoding='utf-8') as f:
        enhanced_content = f.read()
    
    print(f"\n📄 增强后的内容预览:")
    print(enhanced_content[:500] + "..." if len(enhanced_content) > 500 else enhanced_content)

def example_chat_model_ready():
    """生成可直接用于Chat模型的内容"""
    print("\n=== Chat模型就绪示例 ===")
    
    processor = DocumentProcessor()
    
    # 使用专门为Chat模型优化的提示词
    chat_optimized_prompt = """
    请为这张图片生成一个适合AI模型理解的描述，包括：
    1. 图片类型（图表、截图、照片等）
    2. 主要内容和关键信息
    3. 重要的文字或数据
    4. 与上下文相关的细节
    用清晰、结构化的中文描述。
    """
    
    result = processor.process_document(
        file_path="chat_ready_doc.pdf",
        output_dir="./output/chat_ready",
        analyze_images=True,
        image_analysis_prompt=chat_optimized_prompt
    )
    
    # 读取结果并展示如何用于Chat模型
    with open(result['enhanced_markdown'], 'r', encoding='utf-8') as f:
        chat_ready_content = f.read()
    
    print("✅ 内容已准备就绪，可以直接发送给Chat模型:")
    print("=" * 50)
    print("以下是处理后的文档内容，请帮我分析其中的关键信息：")
    print()
    print(chat_ready_content[:1000] + "..." if len(chat_ready_content) > 1000 else chat_ready_content)
    print("=" * 50)

if __name__ == "__main__":
    # 设置OpenAI API密钥（如果没有在环境变量中设置）
    # os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    
    # 运行示例
    try:
        example_basic_usage()
        example_custom_prompt()
        example_no_image_analysis()
        example_result_analysis()
        example_chat_model_ready()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("请确保:")
        print("1. 已安装所需依赖: pip install marker-pdf openai")
        print("2. 已设置OpenAI API密钥")
        print("3. 示例文档文件存在")
