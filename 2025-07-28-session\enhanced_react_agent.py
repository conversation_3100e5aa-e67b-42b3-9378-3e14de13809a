#!/usr/bin/env python3
"""
增强版ReAct Agent - 渐进式反馈模式
展示更好的用户体验设计
"""

from openai import AsyncOpenAI
import os
import asyncio
import json
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class ProgressTracker:
    """进度跟踪器 - 智能管理任务进展"""
    
    def __init__(self):
        self.completed_actions = []
        self.current_phase = ""
        self.total_estimated_steps = 0
    
    def add_completed_action(self, action: str, result_summary: str):
        """添加已完成的行动"""
        self.completed_actions.append({
            "action": action,
            "result": result_summary,
            "timestamp": datetime.now().strftime('%H:%M:%S')
        })
    
    def get_progress_summary(self) -> str:
        """获取进度概要"""
        if not self.completed_actions:
            return "任务刚开始"
        
        recent_actions = self.completed_actions[-2:]  # 最近2个行动
        summary = " → ".join([f"{a['action']}({a['result']})" for a in recent_actions])
        return f"已完成 {len(self.completed_actions)} 步：{summary}"
    
    def generate_progress_display(self) -> str:
        """生成用户友好的进度显示"""
        if not self.completed_actions:
            return "🚀 任务启动中..."
        
        progress_bar = "█" * len(self.completed_actions) + "░" * max(0, 5 - len(self.completed_actions))
        return f"📊 进度 [{progress_bar}] {len(self.completed_actions)}/? 步完成"

# 全局进度跟踪器
progress_tracker = ProgressTracker()

async def enhanced_think_and_plan(
    user_intent: str, 
    current_situation: str, 
    plan: list, 
    next_action: str,
    context_hint: str = ""  # 新增：上下文提示
) -> str:
    """
    增强版思考规划工具 - 渐进式反馈
    
    核心改进：
    1. 智能进度跟踪，而非刚性步骤编号
    2. 上下文感知的动态调整
    3. 用户友好的进展反馈
    """
    
    # 获取当前进度
    progress_summary = progress_tracker.get_progress_summary()
    progress_display = progress_tracker.generate_progress_display()
    
    planning_result = {
        "reasoning": {
            "用户意图": user_intent,
            "当前状况": current_situation,
            "已完成概要": progress_summary,
            "执行计划": plan,
            "上下文提示": context_hint
        },
        "acting": {
            "下一步行动": next_action,
            "预期结果": f"完成后将推进任务向 '{user_intent}' 目标前进",
            "风险评估": "低风险，可安全执行"
        },
        "progress": {
            "当前阶段": progress_tracker.current_phase or "执行阶段",
            "进度显示": progress_display,
            "预计剩余": "根据执行情况动态调整"
        }
    }
    
    # 生成智能反馈
    if progress_tracker.completed_actions:
        feedback = f"""
🎯 任务进展更新
{progress_display}
✅ 最近完成：{progress_tracker.completed_actions[-1]['action']}
🔄 正在执行：{next_action}
💡 智能提示：{context_hint or '继续按计划执行'}
"""
    else:
        feedback = f"""
🚀 任务启动
🎯 目标：{user_intent}
🔄 首步行动：{next_action}
💡 策略：{context_hint or '智能规划，动态调整'}
"""
    
    print(feedback)
    return json.dumps(planning_result, ensure_ascii=False, indent=2)

async def mock_search_tool(query: str) -> str:
    """模拟搜索工具"""
    await asyncio.sleep(1)  # 模拟网络延迟
    result = f"找到关于'{query}'的相关信息：这是模拟的搜索结果。"
    
    # 更新进度跟踪
    progress_tracker.add_completed_action(
        action=f"搜索'{query}'",
        result_summary="获得相关信息"
    )
    
    return result

async def demo_enhanced_workflow():
    """演示增强版工作流程"""
    print("=" * 60)
    print("🚀 增强版ReAct Agent演示 - 渐进式反馈模式")
    print("=" * 60)
    
    # 第一步：初始规划
    await enhanced_think_and_plan(
        user_intent="了解AI Agent的最新发展趋势",
        current_situation="用户询问AI Agent发展趋势，需要搜索最新信息",
        plan=["搜索AI Agent最新论文", "分析技术趋势", "总结关键发现"],
        next_action="搜索AI Agent 2024年最新研究",
        context_hint="重点关注ReAct、Planning等关键技术"
    )
    
    # 模拟执行搜索
    await asyncio.sleep(2)
    search_result = await mock_search_tool("AI Agent 2024年最新研究")
    
    # 第二步：基于结果的重新规划
    await enhanced_think_and_plan(
        user_intent="了解AI Agent的最新发展趋势",
        current_situation="已获得初步搜索结果，需要深入分析特定技术",
        plan=["深入搜索ReAct模式", "对比不同Agent架构", "生成趋势报告"],
        next_action="深入搜索ReAct模式的最新进展",
        context_hint="基于初步结果，聚焦ReAct技术细节"
    )
    
    # 继续模拟执行
    await asyncio.sleep(2)
    await mock_search_tool("ReAct模式最新进展")
    
    # 第三步：总结阶段
    await enhanced_think_and_plan(
        user_intent="了解AI Agent的最新发展趋势",
        current_situation="已收集足够信息，准备生成最终报告",
        plan=["整理收集的信息", "生成趋势分析", "提供实用建议"],
        next_action="生成AI Agent发展趋势报告",
        context_hint="重点突出实用价值和未来方向"
    )
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    print("💡 关键优势：")
    print("   - 智能进度跟踪，无需预设步骤")
    print("   - 上下文感知的动态调整")
    print("   - 用户友好的进展反馈")
    print("   - 保持执行灵活性")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(demo_enhanced_workflow())
