# Ollama本地多模态图片分析器

使用Ollama本地部署的qwen2.5-vl:7b多模态模型进行图片分析和问答的Python工具。

## ✨ 特性

- 🖼️ **本地图片分析**: 支持多种图片格式 (jpg, png, gif, bmp, webp)
- 🤖 **智能问答**: 可以回答关于图片内容的任何问题
- 🔄 **流式输出**: 支持实时查看模型生成过程
- 📦 **批量处理**: 一次性处理多张图片
- 💬 **交互模式**: 友好的命令行交互界面
- 📊 **性能监控**: 显示响应时间和生成速度
- 🛡️ **错误处理**: 完善的异常处理和用户提示

## 🚀 快速开始

### 1. 环境准备

确保已安装并运行Ollama：

```bash
# 安装Ollama (如果还没安装)
curl -fsSL https://ollama.ai/install.sh | sh

# 启动Ollama服务
ollama serve

# 下载qwen2.5-vl:7b模型
ollama pull qwen2.5-vl:7b
```

### 2. 安装Python依赖

```bash
pip install -r ollama_requirements.txt
```

### 3. 基础使用

```bash
# 交互式模式（推荐）
python ollama_vision_analyzer.py --interactive

# 分析单张图片
python ollama_vision_analyzer.py --image "path/to/your/image.jpg" --question "请描述这张图片"

# 使用流式输出
python ollama_vision_analyzer.py --image "image.jpg" --stream

# 批量处理
python ollama_vision_analyzer.py --batch image1.jpg image2.png image3.jpg --output results.json
```

## 📖 详细使用说明

### 交互式模式

最推荐的使用方式，提供友好的命令行界面：

```bash
python ollama_vision_analyzer.py --interactive
```

在交互模式中：
- 输入图片路径
- 输入你的问题（可以是任何关于图片的问题）
- 选择是否使用流式输出
- 查看分析结果和性能统计

### 命令行参数

```bash
python ollama_vision_analyzer.py [选项]

选项:
  --model MODEL         模型名称 (默认: qwen2.5-vl:7b)
  --host HOST          Ollama服务地址 (默认: http://localhost:11434)
  --image IMAGE        图片路径
  --question QUESTION  问题 (默认: "请详细描述这张图片的内容")
  --stream            使用流式输出
  --batch IMAGE [IMAGE ...] 批量处理图片路径
  --output OUTPUT      批量处理结果输出文件
  --interactive       交互式模式
  --help              显示帮助信息
```

### Python API使用

```python
from ollama_vision_analyzer import OllamaVisionAnalyzer

# 初始化分析器
analyzer = OllamaVisionAnalyzer()

# 分析单张图片
result = analyzer.analyze_image(
    image_path="your_image.jpg",
    question="这张图片中有什么？"
)

if result['success']:
    print(f"回答: {result['response']}")
else:
    print(f"错误: {result['error']}")

# 批量处理
results = analyzer.batch_analyze(
    image_paths=["img1.jpg", "img2.png"],
    questions=["描述图片", "提取文字"],
    output_file="results.json"
)
```

## 🎯 使用场景示例

### 1. 图片内容描述
```python
question = "请详细描述这张图片的内容，包括主要物体、场景和活动"
```

### 2. OCR文字识别
```python
question = "请提取图片中的所有文字内容，按从上到下、从左到右的顺序"
```

### 3. 图表数据分析
```python
question = "这是什么类型的图表？显示了什么数据趋势？"
```

### 4. 情感和氛围分析
```python
question = "这张图片传达了什么情感或氛围？"
```

### 5. 技术图解分析
```python
question = "请分析这张技术架构图，说明各个组件的关系"
```

### 6. 产品图片分析
```python
question = "描述这个产品的外观特征、材质和设计风格"
```

## 🔧 高级配置

### 自定义模型

如果你使用其他多模态模型：

```bash
python ollama_vision_analyzer.py --model "llava:7b" --interactive
```

### 自定义Ollama服务地址

如果Ollama运行在其他地址：

```bash
python ollama_vision_analyzer.py --host "http://*************:11434" --interactive
```

### 批量处理配置

```python
# 为不同图片使用不同问题
analyzer = OllamaVisionAnalyzer()
results = analyzer.batch_analyze(
    image_paths=["chart.png", "photo.jpg", "document.png"],
    questions=[
        "分析这个图表的数据",
        "描述照片中的场景", 
        "提取文档中的文字"
    ],
    output_file="analysis_results.json"
)
```

## 📊 性能优化建议

### 1. 模型选择
- **qwen2.5-vl:7b**: 平衡性能和质量，推荐日常使用
- **llava:7b**: 更快的推理速度，适合批量处理
- **llava:13b**: 更高的分析质量，需要更多GPU内存

### 2. 硬件要求
- **最低配置**: 8GB RAM, 4GB GPU内存
- **推荐配置**: 16GB RAM, 8GB GPU内存
- **批量处理**: 32GB RAM, 12GB+ GPU内存

### 3. 优化技巧
```python
# 对于大批量处理，可以预热模型
analyzer = OllamaVisionAnalyzer()
# 先处理一张小图片预热
analyzer.analyze_image("small_test.jpg", "test")

# 然后进行批量处理
results = analyzer.batch_analyze(large_image_list)
```

## 🛠️ 故障排除

### 常见问题

**1. 模型未找到**
```
❌ 模型 qwen2.5-vl:7b 未找到
```
解决方案：
```bash
ollama pull qwen2.5-vl:7b
```

**2. 连接Ollama服务失败**
```
❌ 连接Ollama服务失败
```
解决方案：
```bash
# 启动Ollama服务
ollama serve

# 检查服务状态
curl http://localhost:11434/api/tags
```

**3. 图片格式不支持**
```
❌ 不支持的图片格式: .tiff
```
解决方案：使用支持的格式 (jpg, png, gif, bmp, webp) 或转换图片格式

**4. 内存不足**
```
❌ CUDA out of memory
```
解决方案：
- 使用更小的模型
- 减少批量处理的图片数量
- 增加GPU内存或使用CPU模式

### 调试模式

如果遇到问题，可以查看详细错误信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

analyzer = OllamaVisionAnalyzer()
result = analyzer.analyze_image("problem_image.jpg", "test question")
```

## 📝 示例代码

运行完整的示例演示：

```bash
python ollama_vision_examples.py
```

这将展示：
- 基础使用方法
- 不同类型的问题示例
- 流式输出演示
- 批量处理示例
- OCR文字识别
- 图表分析
- 性能测试
- 错误处理

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

**💡 提示**: 这个工具完全在本地运行，不需要互联网连接，保护你的数据隐私！
