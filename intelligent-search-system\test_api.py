#!/usr/bin/env python3
"""
测试API搜索功能
"""

import requests
import json

def test_search():
    """测试搜索API"""
    url = "http://127.0.0.1:8000/api/v1/search"
    
    data = {
        "query": "智谱轻言最近出了什么新的模型",
        "max_results": 3,
        "use_react_mode": True,
        "include_answer": True
    }
    
    print(f"发送请求到: {url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=data, timeout=60)
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n搜索成功!")
            print(f"查询: {result.get('query')}")
            print(f"答案: {result.get('answer', '无答案')}")
            print(f"结果数量: {result.get('total_results', 0)}")
            print(f"执行时间: {result.get('execution_time_ms', 0)}ms")
        else:
            print(f"\n请求失败:")
            print(response.text)
            
    except Exception as e:
        print(f"\n请求异常: {e}")

if __name__ == "__main__":
    test_search()
