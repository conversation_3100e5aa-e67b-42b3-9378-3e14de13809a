"""
搜索功能测试

测试搜索引擎和相关功能。
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.search import SearchEngine, SearchResult, SearchResponse
from src.core.planning import PlanningEngine
from src.utils.logging import DualLogger
from src.utils.exceptions import APIKeyError, ExternalServiceError


class TestSearchEngine:
    """搜索引擎测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.logger = DualLogger("test_session")
        self.search_engine = SearchEngine(self.logger)
    
    def test_search_engine_creation(self):
        """测试搜索引擎创建"""
        assert self.search_engine is not None
        assert hasattr(self.search_engine, 'search')
        assert hasattr(self.search_engine, 'search_to_json')
    
    @patch('requests.post')
    @pytest.mark.asyncio
    async def test_tavily_search_success(self, mock_post):
        """测试Tavily搜索成功情况"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "query": "test query",
            "answer": "test answer",
            "results": [
                {
                    "title": "Test Title",
                    "url": "https://example.com",
                    "content": "Test content",
                    "score": 0.95
                }
            ]
        }
        mock_post.return_value = mock_response
        
        # 执行搜索
        result = await self.search_engine.search("test query")
        
        # 验证结果
        assert isinstance(result, SearchResponse)
        assert result.query == "test query"
        assert result.answer == "test answer"
        assert len(result.results) == 1
        assert result.results[0].title == "Test Title"
    
    @patch('requests.post')
    @pytest.mark.asyncio
    async def test_tavily_search_api_error(self, mock_post):
        """测试Tavily搜索API错误"""
        # 模拟API错误响应
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = "Unauthorized"
        mock_post.return_value = mock_response
        
        # 执行搜索，应该抛出异常
        with pytest.raises(APIKeyError):
            await self.search_engine.search("test query")
    
    @patch('requests.post')
    @pytest.mark.asyncio
    async def test_tavily_search_network_error(self, mock_post):
        """测试网络错误"""
        # 模拟网络异常
        mock_post.side_effect = Exception("Network error")
        
        # 执行搜索，应该抛出异常
        with pytest.raises(ExternalServiceError):
            await self.search_engine.search("test query")
    
    def test_search_to_json(self):
        """测试搜索结果JSON序列化"""
        # 创建模拟的搜索响应
        with patch.object(self.search_engine, 'search') as mock_search:
            mock_result = SearchResponse(
                query="test",
                answer="answer",
                results=[],
                total_results=0
            )
            mock_search.return_value = mock_result
            
            # 测试JSON序列化
            json_result = self.search_engine.search_to_json("test")
            assert isinstance(json_result, str)
            
            # 验证JSON格式
            parsed = json.loads(json_result)
            assert parsed["query"] == "test"
            assert parsed["answer"] == "answer"


class TestPlanningEngine:
    """规划引擎测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.logger = DualLogger("test_session")
        self.planning_engine = PlanningEngine(self.logger)
    
    def test_planning_engine_creation(self):
        """测试规划引擎创建"""
        assert self.planning_engine is not None
        assert hasattr(self.planning_engine, 'think_and_plan')
        assert hasattr(self.planning_engine, 'think_and_plan_to_json')
    
    @pytest.mark.asyncio
    async def test_think_and_plan(self):
        """测试思考和规划功能"""
        result = await self.planning_engine.think_and_plan(
            user_intent="搜索信息",
            current_situation="用户想要搜索",
            plan=["分析查询", "执行搜索", "返回结果"],
            next_action="开始搜索"
        )
        
        assert isinstance(result, str)
        assert "用户意图" in result
        assert "执行计划" in result
    
    def test_think_and_plan_to_json(self):
        """测试规划结果JSON序列化"""
        json_result = self.planning_engine.think_and_plan_to_json(
            user_intent="test intent",
            current_situation="test situation", 
            plan=["step1", "step2"],
            next_action="test action"
        )
        
        assert isinstance(json_result, str)
        
        # 验证JSON格式
        parsed = json.loads(json_result)
        assert "思考过程" in parsed
        assert "规划结果" in parsed


class TestSearchResult:
    """搜索结果测试"""
    
    def test_search_result_creation(self):
        """测试搜索结果创建"""
        result = SearchResult(
            title="Test Title",
            url="https://example.com",
            content="Test content",
            score=0.95
        )
        
        assert result.title == "Test Title"
        assert result.url == "https://example.com"
        assert result.content == "Test content"
        assert result.score == 0.95
    
    def test_search_result_dict_conversion(self):
        """测试搜索结果字典转换"""
        result = SearchResult(
            title="Test Title",
            url="https://example.com", 
            content="Test content",
            score=0.95
        )
        
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert result_dict["title"] == "Test Title"
        assert result_dict["url"] == "https://example.com"


class TestSearchResponse:
    """搜索响应测试"""
    
    def test_search_response_creation(self):
        """测试搜索响应创建"""
        results = [
            SearchResult("Title 1", "https://example1.com", "Content 1", 0.9),
            SearchResult("Title 2", "https://example2.com", "Content 2", 0.8)
        ]
        
        response = SearchResponse(
            query="test query",
            answer="test answer",
            results=results,
            total_results=2
        )
        
        assert response.query == "test query"
        assert response.answer == "test answer"
        assert len(response.results) == 2
        assert response.total_results == 2
    
    def test_search_response_dict_conversion(self):
        """测试搜索响应字典转换"""
        results = [
            SearchResult("Title", "https://example.com", "Content", 0.9)
        ]
        
        response = SearchResponse(
            query="test",
            answer="answer",
            results=results,
            total_results=1
        )
        
        response_dict = response.to_dict()
        assert isinstance(response_dict, dict)
        assert response_dict["query"] == "test"
        assert response_dict["answer"] == "answer"
        assert len(response_dict["results"]) == 1


class TestIntegrationSearch:
    """搜索集成测试"""
    
    def test_imports(self):
        """测试模块导入"""
        try:
            from src.core.search import SearchEngine
            from src.core.planning import PlanningEngine
            from src.utils.logging import DualLogger
            
            assert SearchEngine is not None
            assert PlanningEngine is not None
            assert DualLogger is not None
            
        except ImportError as e:
            pytest.fail(f"模块导入失败: {e}")
    
    def test_environment_variables(self):
        """测试环境变量"""
        import os
        
        # 检查是否有API密钥（测试环境可能没有）
        openai_key = os.getenv("OPENAI_API_KEY")
        tavily_key = os.getenv("TAVILY_API_KEY")
        
        if not openai_key:
            pytest.skip("OPENAI_API_KEY未设置，跳过相关测试")
        
        if not tavily_key:
            pytest.skip("TAVILY_API_KEY未设置，跳过相关测试")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
