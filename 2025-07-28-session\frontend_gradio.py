#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gradio前端界面 - 智能搜索Web应用
连接FastAPI后端，提供用户友好的搜索界面
"""

import gradio as gr
import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# API配置
API_BASE_URL = "http://localhost:8000"

class SearchClient:
    """搜索客户端"""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
    
    def search(self, query: str, max_results: int = 5, 
               include_answer: bool = True, use_react_mode: bool = True) -> Dict:
        """调用搜索API"""
        try:
            response = requests.post(
                f"{self.base_url}/search",
                json={
                    "query": query,
                    "max_results": max_results,
                    "include_answer": include_answer,
                    "use_react_mode": use_react_mode
                },
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API调用失败: {response.status_code}"}
                
        except requests.exceptions.RequestException as e:
            return {"error": f"网络请求失败: {str(e)}"}
    
    def get_logs(self, session_id: str) -> Dict:
        """获取详细日志"""
        try:
            response = requests.get(f"{self.base_url}/logs/{session_id}")
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": "获取日志失败"}
        except:
            return {"error": "网络错误"}

# 初始化客户端
client = SearchClient()

def format_search_results(results: List[Dict]) -> str:
    """格式化搜索结果"""
    if not results:
        return "❌ 没有找到相关结果"
    
    formatted = []
    for i, result in enumerate(results, 1):
        formatted.append(f"""
### 📄 结果 {i}: {result['title']}
**🔗 链接**: [{result['url']}]({result['url']})
**📊 相关度**: {result['score']:.2f}
**📝 摘要**: {result['content'][:200]}...
---
""")
    
    return "\n".join(formatted)

def format_logs(logs_data: Dict) -> str:
    """格式化日志信息"""
    if "error" in logs_data:
        return f"❌ {logs_data['error']}"
    
    steps = logs_data.get("steps", [])
    if not steps:
        return "📝 暂无执行日志"
    
    formatted = [f"# 🔍 执行日志 (会话ID: {logs_data['session_id']})\n"]
    
    for step in steps:
        status_icon = "✅" if step['status'] == 'COMPLETED' else "🔄"
        formatted.append(f"""
## {status_icon} {step['step_name']}
- **状态**: {step['status']}
- **开始时间**: {step['start_time']}
- **耗时**: {step.get('duration_ms', 0)}ms
- **输入数据**: {json.dumps(step.get('input_data', {}), ensure_ascii=False, indent=2)}
""")
        
        if step.get('error_info'):
            formatted.append(f"- **错误信息**: {step['error_info']}")
    
    return "\n".join(formatted)

def perform_search(query: str, max_results: int, include_answer: bool, 
                  use_react_mode: bool) -> Tuple[str, str, str, str]:
    """执行搜索并返回结果"""
    if not query.strip():
        return "❌ 请输入搜索关键词", "", "", ""
    
    # 显示搜索开始状态
    start_time = time.time()
    
    # 调用API
    result = client.search(
        query=query,
        max_results=max_results,
        include_answer=include_answer,
        use_react_mode=use_react_mode
    )
    
    if "error" in result:
        return f"❌ 搜索失败: {result['error']}", "", "", ""
    
    # 格式化结果
    execution_time = time.time() - start_time
    
    # AI答案
    ai_answer = result.get("answer", "暂无AI生成的答案")
    if ai_answer:
        ai_answer_formatted = f"""
# 🤖 AI智能答案

{ai_answer}

---
**⚡ 执行时间**: {result.get('execution_time', execution_time):.2f}秒
**📊 完成步骤**: {result.get('steps_completed', 0)}步
**🆔 会话ID**: {result.get('session_id', 'unknown')}
"""
    else:
        ai_answer_formatted = "🤖 暂无AI生成的答案"
    
    # 搜索结果
    search_results_formatted = format_search_results(result.get("results", []))
    
    # 获取详细日志
    session_id = result.get("session_id", "")
    logs_data = client.get_logs(session_id) if session_id else {}
    logs_formatted = format_logs(logs_data)
    
    # 执行统计
    stats = f"""
# 📊 执行统计

- **搜索查询**: {result.get('query', query)}
- **结果数量**: {len(result.get('results', []))}个
- **执行模式**: {'ReAct智能模式' if use_react_mode else '直接搜索模式'}
- **执行时间**: {result.get('execution_time', execution_time):.2f}秒
- **完成步骤**: {result.get('steps_completed', 0)}步
- **会话ID**: {result.get('session_id', 'unknown')}
- **时间戳**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    return ai_answer_formatted, search_results_formatted, logs_formatted, stats

# 创建Gradio界面
def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(
        title="🔍 智能搜索系统",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .search-header {
            text-align: center;
            padding: 20px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        """
    ) as interface:
        
        # 标题和说明
        gr.HTML("""
        <div class="search-header">
            <h1>🔍 智能搜索系统</h1>
            <p>基于ReAct模式的智能搜索，集成双层日志系统</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # 搜索输入区域
                gr.Markdown("## 🎯 搜索配置")
                
                query_input = gr.Textbox(
                    label="🔍 搜索关键词",
                    placeholder="请输入您要搜索的内容...",
                    lines=2
                )
                
                with gr.Row():
                    max_results = gr.Slider(
                        label="📊 结果数量",
                        minimum=1,
                        maximum=20,
                        value=5,
                        step=1
                    )
                    
                    include_answer = gr.Checkbox(
                        label="🤖 包含AI答案",
                        value=True
                    )
                    
                    use_react_mode = gr.Checkbox(
                        label="🧠 启用ReAct智能模式",
                        value=True
                    )
                
                search_btn = gr.Button(
                    "🚀 开始搜索",
                    variant="primary",
                    size="lg"
                )
                
                # 示例查询
                gr.Markdown("### 💡 示例查询")
                examples = gr.Examples(
                    examples=[
                        ["AI Agent最新发展趋势"],
                        ["黑神话悟空发售时间"],
                        ["Python FastAPI教程"],
                        ["机器学习算法对比"],
                        ["区块链技术应用"]
                    ],
                    inputs=[query_input]
                )
            
            with gr.Column(scale=2):
                # 结果显示区域
                gr.Markdown("## 📋 搜索结果")
                
                with gr.Tabs():
                    with gr.TabItem("🤖 AI智能答案"):
                        ai_answer_output = gr.Markdown(
                            value="等待搜索结果...",
                            height=400
                        )
                    
                    with gr.TabItem("📄 详细结果"):
                        search_results_output = gr.Markdown(
                            value="等待搜索结果...",
                            height=400
                        )
                    
                    with gr.TabItem("📊 执行统计"):
                        stats_output = gr.Markdown(
                            value="等待执行统计...",
                            height=400
                        )
                    
                    with gr.TabItem("🔧 开发者日志"):
                        logs_output = gr.Markdown(
                            value="等待执行日志...",
                            height=400
                        )
        
        # 绑定搜索事件
        search_btn.click(
            fn=perform_search,
            inputs=[query_input, max_results, include_answer, use_react_mode],
            outputs=[ai_answer_output, search_results_output, logs_output, stats_output]
        )
        
        # 回车键搜索
        query_input.submit(
            fn=perform_search,
            inputs=[query_input, max_results, include_answer, use_react_mode],
            outputs=[ai_answer_output, search_results_output, logs_output, stats_output]
        )
        
        # 页脚信息
        gr.HTML("""
        <div style="text-align: center; padding: 20px; color: #666;">
            <p>🔧 技术栈: FastAPI + Gradio + ReAct + Tavily Search</p>
            <p>📚 集成双层日志系统，支持用户友好界面和开发者调试</p>
        </div>
        """)
    
    return interface

if __name__ == "__main__":
    print("🚀 启动智能搜索前端界面")
    print("🌐 前端地址: http://localhost:7860")
    print("📡 后端API: http://localhost:8000")
    print("=" * 50)
    
    # 创建并启动界面
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        show_tips=True
    )
