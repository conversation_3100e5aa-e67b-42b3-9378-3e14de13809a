# 🚀 快速开始指南

## 📁 项目已就绪

所有多模态相关的代码和配置文件已经整理到 `J:\LLM-TT\多模态测试` 目录中。

## 🎯 一键启动

```bash
cd "J:\LLM-TT\多模态测试"
python start.py
```

启动脚本会自动：
- 🔍 检查环境和依赖
- 📋 显示方案选择菜单
- 🚀 引导你完成配置和使用

## 📦 文件说明

### 🤖 Ollama本地方案
- `ollama_vision_analyzer.py` - 主程序
- `ollama_vision_examples.py` - 使用示例
- `OLLAMA_VISION_README.md` - 详细文档

### 📄 文档处理方案  
- `document_processor.py` - 主程序
- `example_usage.py` - 使用示例
- `DOCUMENT_PROCESSOR_README.md` - 详细文档

### 📚 通用文件
- `README.md` - 项目总览
- `requirements.txt` - 统一依赖管理
- `start.py` - 一键启动脚本
- `快速开始.md` - 本文件

## ⚡ 快速测试

### 方案一：Ollama本地分析
```bash
# 1. 启动Ollama服务
ollama serve

# 2. 下载模型
ollama pull qwen2.5-vl:7b

# 3. 运行分析器
python ollama_vision_analyzer.py --interactive
```

### 方案二：文档处理
```bash
# 1. 设置API密钥
export OPENAI_API_KEY="your-key"

# 2. 运行示例
python example_usage.py
```

## 🛠️ 依赖安装

```bash
# 基础依赖（仅Ollama）
pip install ollama pillow tqdm rich

# 完整依赖（包含文档处理）
pip install -r requirements.txt
```

## 💡 使用建议

1. **首次使用**：运行 `python start.py` 获得引导
2. **Ollama方案**：适合本地化、隐私保护场景
3. **文档处理方案**：适合高质量文档分析需求
4. **遇到问题**：查看对应的详细README文档

---

**🎉 现在你可以开始使用多模态功能了！**
