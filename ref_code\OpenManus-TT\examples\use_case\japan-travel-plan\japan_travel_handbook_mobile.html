<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Japan Travel Guide (Mobile)</title>
    <style>
        * { box-sizing: border-box; }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            margin: 0;
            padding: 10px;
            line-height: 1.6;
            font-size: 16px;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        h1 { font-size: 1.5em; margin: 10px 0; }
        h2 { font-size: 1.3em; margin: 8px 0; }
        h3 { font-size: 1.1em; margin: 6px 0; }

        /* Mobile-friendly cards */
        .card {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin: 10px 0;
            padding: 15px;
        }

        /* Collapsible sections */
        .collapsible {
            background: #f8f9fa;
            border: none;
            border-radius: 8px;
            width: 100%;
            padding: 15px;
            text-align: left;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            margin: 5px 0;
        }

        .content {
            display: none;
            padding: 10px;
        }

        .active {
            background: #e9ecef;
        }

        /* Mobile-friendly tables */
        .table-wrapper {
            overflow-x: auto;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 300px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background: #f8f9fa;
        }

        /* Touch-friendly lists */
        ul, ol {
            padding-left: 20px;
            margin: 10px 0;
        }
        li {
            margin: 8px 0;
            padding: 5px 0;
        }

        /* Emergency info styling */
        .emergency {
            background: #ffe6e6;
            border-left: 4px solid #ff4444;
            padding: 10px;
            margin: 10px 0;
        }

        /* Quick access buttons */
        .quick-access {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .quick-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            font-size: 0.9em;
            cursor: pointer;
            flex: 1 1 auto;
            text-align: center;
            min-width: 120px;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
                color: #fff;
            }
            .card {
                background: #2d2d2d;
            }
            .collapsible {
                background: #333;
                color: #fff;
            }
            .active {
                background: #404040;
            }
            th {
                background: #333;
            }
            td, th {
                border-color: #404040;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Japan Travel Guide</h1>
        <p><strong>April 15-23, 2024</strong></p>

        <div class="quick-access">
            <button class="quick-btn" onclick="showSection('emergency')">Emergency</button>
            <button class="quick-btn" onclick="showSection('phrases')">Phrases</button>
            <button class="quick-btn" onclick="showSection('transport')">Transport</button>
            <button class="quick-btn" onclick="showSection('proposal')">Proposal</button>
        </div>

        <div class="emergency card" id="emergency">
            <h2>Emergency Contacts</h2>
            <ul>
                <li>🚑 Emergency: 119</li>
                <li>👮 Police: 110</li>
                <li>🏢 US Embassy: +81-3-3224-5000</li>
                <li>ℹ️ Tourist Info: 03-3201-3331</li>
            </ul>
        </div>

        <button class="collapsible">📅 Daily Itinerary</button>
        <div class="content">
            <div class="table-wrapper">
                <table>
                    <tr><th>Date</th><th>Location</th><th>Activities</th></tr>
                    <tr><td>Apr 15</td><td>Tokyo</td><td>Arrival, Shinjuku</td></tr>
                    <tr><td>Apr 16</td><td>Tokyo</td><td>Meiji, Harajuku, Senso-ji</td></tr>
                    <tr><td>Apr 17</td><td>Tokyo</td><td>Tea Ceremony, Budokan</td></tr>
                    <tr><td>Apr 18</td><td>Kyoto</td><td>Travel, Kinkaku-ji</td></tr>
                    <tr><td>Apr 19</td><td>Kyoto</td><td>Fushimi Inari, Proposal</td></tr>
                    <tr><td>Apr 20</td><td>Nara</td><td>Deer Park, Temples</td></tr>
                    <tr><td>Apr 21</td><td>Tokyo</td><td>Return, Bay Cruise</td></tr>
                </table>
            </div>
        </div>

        <button class="collapsible">🗣️ Essential Phrases</button>
        <div class="content">
            <div class="table-wrapper">
                <table>
                    <tr><th>English</th><th>Japanese</th></tr>
                    <tr><td>Thank you</td><td>ありがとう</td></tr>
                    <tr><td>Excuse me</td><td>すみません</td></tr>
                    <tr><td>Please</td><td>お願いします</td></tr>
                    <tr><td>Where is...</td><td>...はどこですか</td></tr>
                    <tr><td>Help!</td><td>助けて!</td></tr>
                </table>
            </div>
        </div>

        <button class="collapsible">🚅 Transportation</button>
        <div class="content">
            <div class="card">
                <h3>Key Routes</h3>
                <ul>
                    <li>Tokyo-Kyoto: 2h15m</li>
                    <li>Kyoto-Nara: 45m</li>
                    <li>Last trains: ~midnight</li>
                </ul>
                <p><strong>JR Pass:</strong> Activate April 15</p>
            </div>
        </div>

        <button class="collapsible">💍 Proposal Plan</button>
        <div class="content">
            <div class="card">
                <h3>April 19 Timeline</h3>
                <ul>
                    <li>4:00 PM: Head to Maruyama Park</li>
                    <li>5:30 PM: Arrive at spot</li>
                    <li>7:00 PM: Dinner at Kikunoi Roan</li>
                </ul>
                <p><strong>Backup:</strong> Gion Shirakawa area</p>
            </div>
        </div>

        <button class="collapsible">💰 Budget Tracker</button>
        <div class="content">
            <div class="table-wrapper">
                <table>
                    <tr><th>Item</th><th>Budget</th></tr>
                    <tr><td>Hotels</td><td>$1500-2000</td></tr>
                    <tr><td>Transport</td><td>$600-800</td></tr>
                    <tr><td>Food</td><td>$800-1000</td></tr>
                    <tr><td>Activities</td><td>$600-800</td></tr>
                    <tr><td>Shopping</td><td>$500-400</td></tr>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Add click handlers for collapsible sections
        var coll = document.getElementsByClassName("collapsible");
        for (var i = 0; i < coll.length; i++) {
            coll[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var content = this.nextElementSibling;
                if (content.style.display === "block") {
                    content.style.display = "none";
                } else {
                    content.style.display = "block";
                }
            });
        }

        // Function to show specific section
        function showSection(id) {
            document.getElementById(id).scrollIntoView({
                behavior: 'smooth'
            });
        }
    </script>
</body>
</html>
