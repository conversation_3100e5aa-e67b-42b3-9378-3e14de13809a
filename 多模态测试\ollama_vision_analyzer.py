#!/usr/bin/env python3
"""
Ollama本地多模态图片分析器
使用qwen2.5-vl:7b模型对图片进行分析和问答
"""

import os
import base64
import json
from pathlib import Path
from typing import Optional, Dict, Any
import argparse

try:
    from ollama import Client
    import ollama
except ImportError:
    print("❌ 请先安装ollama库: pip install ollama")
    exit(1)

class OllamaVisionAnalyzer:
    def __init__(self, 
                 model_name: str = "qwen2.5-vl:7b",
                 host: str = "http://localhost:11434"):
        """
        初始化Ollama视觉分析器
        
        Args:
            model_name: 模型名称，默认为qwen2.5-vl:7b
            host: Ollama服务地址
        """
        self.model_name = model_name
        self.client = Client(host=host)
        
        # 检查模型是否可用
        self._check_model_availability()
    
    def _check_model_availability(self):
        """检查模型是否已下载并可用"""
        try:
            models = self.client.list()
            available_models = [model['name'] for model in models['models']]
            
            if self.model_name not in available_models:
                print(f"❌ 模型 {self.model_name} 未找到")
                print(f"📋 可用模型: {', '.join(available_models)}")
                print(f"💡 请先下载模型: ollama pull {self.model_name}")
                exit(1)
            else:
                print(f"✅ 模型 {self.model_name} 已就绪")
                
        except Exception as e:
            print(f"❌ 连接Ollama服务失败: {e}")
            print("💡 请确保Ollama服务正在运行: ollama serve")
            exit(1)
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片文件编码为base64字符串
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            base64编码的图片字符串
        """
        image_path = Path(image_path)
        
        if not image_path.exists():
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 检查文件格式
        supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        if image_path.suffix.lower() not in supported_formats:
            raise ValueError(f"不支持的图片格式: {image_path.suffix}")
        
        try:
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()
                encoded_image = base64.b64encode(image_data).decode('utf-8')
                return encoded_image
        except Exception as e:
            raise Exception(f"图片编码失败: {e}")
    
    def analyze_image(self, 
                     image_path: str, 
                     question: str = "请详细描述这张图片的内容",
                     stream: bool = False) -> Dict[str, Any]:
        """
        分析图片并回答问题
        
        Args:
            image_path: 图片文件路径
            question: 要问的问题
            stream: 是否流式输出
            
        Returns:
            分析结果字典
        """
        print(f"🔍 正在分析图片: {image_path}")
        print(f"❓ 问题: {question}")
        
        try:
            # 编码图片
            encoded_image = self.encode_image(image_path)
            
            # 构建消息
            messages = [
                {
                    'role': 'user',
                    'content': question,
                    'images': [encoded_image],
                }
            ]
            
            # 调用模型
            if stream:
                return self._stream_response(messages)
            else:
                return self._single_response(messages)
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'image_path': image_path,
                'question': question
            }
    
    def _single_response(self, messages) -> Dict[str, Any]:
        """获取单次响应"""
        try:
            response = self.client.chat(
                model=self.model_name,
                messages=messages
            )
            
            return {
                'success': True,
                'response': response['message']['content'],
                'model': self.model_name,
                'created_at': response.get('created_at', ''),
                'total_duration': response.get('total_duration', 0),
                'load_duration': response.get('load_duration', 0),
                'prompt_eval_count': response.get('prompt_eval_count', 0),
                'eval_count': response.get('eval_count', 0)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"模型调用失败: {e}"
            }
    
    def _stream_response(self, messages) -> Dict[str, Any]:
        """获取流式响应"""
        try:
            print("\n🤖 模型回答:")
            print("-" * 50)
            
            full_response = ""
            stream = self.client.chat(
                model=self.model_name,
                messages=messages,
                stream=True
            )
            
            for chunk in stream:
                content = chunk['message']['content']
                print(content, end='', flush=True)
                full_response += content
            
            print("\n" + "-" * 50)
            
            return {
                'success': True,
                'response': full_response,
                'model': self.model_name,
                'stream': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"流式响应失败: {e}"
            }
    
    def batch_analyze(self, 
                     image_paths: list, 
                     questions: list = None,
                     output_file: str = None) -> list:
        """
        批量分析多张图片
        
        Args:
            image_paths: 图片路径列表
            questions: 问题列表，如果为None则使用默认问题
            output_file: 输出文件路径（JSON格式）
            
        Returns:
            分析结果列表
        """
        if questions is None:
            questions = ["请详细描述这张图片的内容"] * len(image_paths)
        
        if len(questions) == 1 and len(image_paths) > 1:
            questions = questions * len(image_paths)
        
        if len(questions) != len(image_paths):
            raise ValueError("问题数量必须与图片数量相等或为1")
        
        results = []
        
        for i, (image_path, question) in enumerate(zip(image_paths, questions), 1):
            print(f"\n📸 处理第 {i}/{len(image_paths)} 张图片")
            result = self.analyze_image(image_path, question)
            result['image_path'] = image_path
            result['question'] = question
            results.append(result)
        
        # 保存结果到文件
        if output_file:
            self._save_results(results, output_file)
        
        return results
    
    def _save_results(self, results: list, output_file: str):
        """保存结果到JSON文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"📄 结果已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def interactive_mode(self):
        """交互式模式"""
        print("🎯 进入交互式模式")
        print("💡 输入 'quit' 或 'exit' 退出")
        print("💡 输入 'help' 查看帮助")
        
        while True:
            try:
                print("\n" + "="*60)
                image_path = input("📸 请输入图片路径: ").strip()
                
                if image_path.lower() in ['quit', 'exit']:
                    print("👋 再见!")
                    break
                
                if image_path.lower() == 'help':
                    self._show_help()
                    continue
                
                if not image_path:
                    print("❌ 请输入有效的图片路径")
                    continue
                
                question = input("❓ 请输入问题 (回车使用默认问题): ").strip()
                if not question:
                    question = "请详细描述这张图片的内容"
                
                stream_input = input("🔄 是否使用流式输出? (y/N): ").strip().lower()
                stream = stream_input in ['y', 'yes', '是']
                
                result = self.analyze_image(image_path, question, stream)
                
                if result['success']:
                    if not stream:
                        print(f"\n🤖 模型回答:")
                        print("-" * 50)
                        print(result['response'])
                        print("-" * 50)
                        
                        # 显示性能信息
                        if 'total_duration' in result:
                            duration_ms = result['total_duration'] / 1000000  # 纳秒转毫秒
                            print(f"⏱️  总耗时: {duration_ms:.2f}ms")
                            if 'eval_count' in result and result['eval_count'] > 0:
                                tokens_per_sec = result['eval_count'] / (duration_ms / 1000)
                                print(f"🚀 生成速度: {tokens_per_sec:.2f} tokens/s")
                else:
                    print(f"❌ 分析失败: {result['error']}")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
🔧 使用帮助:

📸 图片路径:
   - 支持相对路径和绝对路径
   - 支持格式: jpg, jpeg, png, gif, bmp, webp
   - 示例: ./image.jpg, /path/to/image.png

❓ 问题示例:
   - 请详细描述这张图片的内容
   - 这张图片中有什么文字?
   - 分析这张图表的数据趋势
   - 这是什么类型的图片?
   - 图片中的人在做什么?

🔄 流式输出:
   - 选择 'y' 可以实时看到模型生成过程
   - 选择 'N' 等待完整回答后显示

💡 提示:
   - 问题越具体，回答越准确
   - 可以问关于图片内容、文字、颜色、情感等任何方面
   - 支持中文和英文问题
        """
        print(help_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Ollama本地多模态图片分析器")
    parser.add_argument("--model", default="qwen2.5-vl:7b", help="模型名称")
    parser.add_argument("--host", default="http://localhost:11434", help="Ollama服务地址")
    parser.add_argument("--image", help="图片路径")
    parser.add_argument("--question", default="请详细描述这张图片的内容", help="问题")
    parser.add_argument("--stream", action="store_true", help="使用流式输出")
    parser.add_argument("--batch", nargs="+", help="批量处理图片路径")
    parser.add_argument("--output", help="批量处理结果输出文件")
    parser.add_argument("--interactive", action="store_true", help="交互式模式")
    
    args = parser.parse_args()
    
    # 初始化分析器
    analyzer = OllamaVisionAnalyzer(model_name=args.model, host=args.host)
    
    if args.interactive:
        # 交互式模式
        analyzer.interactive_mode()
    elif args.batch:
        # 批量处理模式
        results = analyzer.batch_analyze(args.batch, output_file=args.output)
        print(f"\n✅ 批量处理完成，共处理 {len(results)} 张图片")
    elif args.image:
        # 单张图片处理
        result = analyzer.analyze_image(args.image, args.question, args.stream)
        
        if result['success']:
            if not args.stream:
                print(f"\n🤖 模型回答:")
                print("-" * 50)
                print(result['response'])
                print("-" * 50)
        else:
            print(f"❌ 分析失败: {result['error']}")
    else:
        # 默认进入交互式模式
        print("💡 未指定参数，进入交互式模式")
        analyzer.interactive_mode()


if __name__ == "__main__":
    main()
