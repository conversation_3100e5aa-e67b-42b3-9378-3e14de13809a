# 多模态测试项目

这个项目包含两个主要的多模态文档处理解决方案，可以根据不同需求选择使用。

## 📁 项目结构

```
多模态测试/
├── README.md                           # 项目总览（本文件）
├── requirements.txt                    # 统一依赖管理
│
├── === Ollama本地多模态方案 ===
├── ollama_vision_analyzer.py          # Ollama本地图片分析器
├── ollama_vision_examples.py          # 使用示例和演示
├── ollama_requirements.txt            # Ollama专用依赖
├── OLLAMA_VISION_README.md            # Ollama方案详细文档
│
├── === Marker+OpenAI文档处理方案 ===
├── document_processor.py              # 文档处理器主程序
├── example_usage.py                   # 文档处理使用示例
└── DOCUMENT_PROCESSOR_README.md       # 文档处理方案详细文档
```

## 🎯 两种方案对比

### 方案一：Ollama本地多模态分析

**适用场景：**
- ✅ 需要完全本地化部署
- ✅ 注重数据隐私和安全
- ✅ 有足够的GPU资源（8GB+）
- ✅ 主要处理单张图片分析

**核心特性：**
- 🖼️ 本地图片分析，支持多种格式
- 🤖 使用qwen2.5-vl:7b等开源模型
- 🔄 支持流式输出和批量处理
- 💬 友好的交互式界面
- 📊 性能监控和错误处理

**使用方法：**
```bash
# 启动交互模式
python ollama_vision_analyzer.py --interactive

# 分析单张图片
python ollama_vision_analyzer.py --image "image.jpg" --question "描述图片内容"
```

### 方案二：Marker + OpenAI文档处理

**适用场景：**
- ✅ 需要处理PDF/DOCX文档
- ✅ 要求高质量的图片分析
- ✅ 可以接受API调用成本
- ✅ 需要生成Chat模型可用的增强文档

**核心特性：**
- 📄 支持PDF和DOCX文档转换
- 🖼️ 使用GPT-4V进行图片分析
- 📝 在Markdown中添加图片描述
- 🤖 生成Chat模型友好的内容
- ⚡ 批量处理多个文档

**使用方法：**
```python
from document_processor import DocumentProcessor

processor = DocumentProcessor()
result = processor.process_document(
    file_path="document.pdf",
    analyze_images=True
)
```

## 🚀 快速开始

### 环境准备

1. **安装Python依赖**
```bash
cd "J:\LLM-TT\多模态测试"
pip install -r requirements.txt
```

2. **选择并配置方案**

**方案一（Ollama）：**
```bash
# 安装并启动Ollama
ollama serve
ollama pull qwen2.5-vl:7b

# 测试
python ollama_vision_analyzer.py --interactive
```

**方案二（Marker+OpenAI）：**
```bash
# 设置OpenAI API密钥
export OPENAI_API_KEY="your-api-key"

# 测试
python example_usage.py
```

## 📖 详细使用指南

### Ollama本地方案

查看 `OLLAMA_VISION_README.md` 获取详细说明，包括：
- 完整的安装和配置指南
- 各种使用场景示例
- 性能优化建议
- 故障排除方法

**核心命令：**
```bash
# 交互式使用（推荐）
python ollama_vision_analyzer.py --interactive

# 批量处理
python ollama_vision_analyzer.py --batch *.jpg --output results.json

# 查看所有示例
python ollama_vision_examples.py
```

### 文档处理方案

查看 `DOCUMENT_PROCESSOR_README.md` 获取详细说明，包括：
- 文档转换和图片分析流程
- 自定义提示词配置
- Chat模型集成方法
- 企业部署考虑

**核心用法：**
```python
# 基础文档处理
processor = DocumentProcessor()
result = processor.process_document("document.pdf")

# 带图片分析的处理
result = processor.process_document(
    "document.pdf", 
    analyze_images=True,
    image_analysis_prompt="详细分析图片内容"
)
```

## 🔧 配置和自定义

### 环境变量配置

创建 `.env` 文件：
```bash
# OpenAI配置（方案二）
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4o-mini

# Ollama配置（方案一）
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=qwen2.5-vl:7b

# 通用配置
OUTPUT_DIR=./output
LOG_LEVEL=INFO
```

### 自定义配置

**Ollama模型切换：**
```python
# 使用不同的多模态模型
analyzer = OllamaVisionAnalyzer(model_name="llava:7b")
```

**文档处理自定义：**
```python
# 自定义图片分析提示词
custom_prompt = """
请分析这张图片，重点关注：
1. 技术架构和组件关系
2. 数据流程和接口
3. 关键参数和配置
用技术化的中文描述。
"""

processor = DocumentProcessor()
result = processor.process_document(
    "technical_doc.pdf",
    image_analysis_prompt=custom_prompt
)
```

## 💡 使用建议

### 选择方案的决策树

```
需要处理文档（PDF/DOCX）？
├─ 是 → 使用方案二（Marker+OpenAI）
└─ 否 → 只处理图片？
    ├─ 是 → 考虑数据隐私？
    │   ├─ 重要 → 使用方案一（Ollama本地）
    │   └─ 不重要 → 使用方案二（质量更高）
    └─ 否 → 根据具体需求选择
```

### 性能优化建议

**Ollama方案：**
- 使用SSD存储模型文件
- 确保足够的GPU内存
- 预热模型以提高响应速度

**文档处理方案：**
- 合理控制API调用频率
- 使用更便宜的模型（如gpt-4o-mini）
- 批量处理以提高效率

### 成本控制

**Ollama方案：**
- ✅ 完全免费（除硬件成本）
- ⚠️ 需要高性能GPU

**文档处理方案：**
- ⚠️ API调用有成本
- 💡 可设置 `analyze_images=False` 仅提取图片

## 🛠️ 故障排除

### 常见问题

1. **Ollama连接失败**
   ```bash
   # 检查服务状态
   curl http://localhost:11434/api/tags
   
   # 重启服务
   ollama serve
   ```

2. **模型下载失败**
   ```bash
   # 重新下载模型
   ollama pull qwen2.5-vl:7b
   ```

3. **OpenAI API错误**
   ```bash
   # 检查API密钥
   echo $OPENAI_API_KEY
   
   # 测试连接
   curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
   ```

4. **依赖安装问题**
   ```bash
   # 清理缓存重新安装
   pip cache purge
   pip install -r requirements.txt --no-cache-dir
   ```

### 获取帮助

- 查看具体方案的README文件
- 运行示例代码了解用法
- 检查日志文件获取详细错误信息

## 📞 支持

如有问题，请：
1. 查看对应的详细文档
2. 运行示例代码进行测试
3. 检查环境配置和依赖安装

---

**🎉 现在你可以根据需求选择合适的方案开始使用多模态功能了！**
