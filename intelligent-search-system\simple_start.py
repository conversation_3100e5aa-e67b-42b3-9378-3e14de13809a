#!/usr/bin/env python3
"""
简单启动脚本 - 直接启动API和前端
"""

import sys
import os
import threading
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_api():
    """启动API服务"""
    print("🚀 启动API服务...")
    try:
        import uvicorn
        from src.api.main import app
        
        # 直接运行uvicorn
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        import traceback
        traceback.print_exc()

def start_frontend():
    """启动前端服务"""
    print("🎨 启动前端服务...")
    try:
        # 等待API服务启动
        time.sleep(5)
        
        from src.frontend.app import main as frontend_main
        frontend_main()
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 50)
    print("🔍 智能搜索系统 - 简单启动")
    print("=" * 50)
    
    # 检查依赖
    try:
        from src.utils.config import get_settings
        settings = get_settings()
        print(f"✅ 配置加载成功")
        print(f"   API端口: {settings.api_port}")
        print(f"   前端端口: {settings.frontend_port}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        sys.exit(1)
    
    print("\n启动服务...")
    
    # 启动API服务（在主线程中）
    try:
        start_api()
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
