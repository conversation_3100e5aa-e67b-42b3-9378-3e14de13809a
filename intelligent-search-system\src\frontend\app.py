"""
Gradio前端应用

提供用户友好的Web界面，用于搜索交互和结果展示。
"""

import gradio as gr
import requests
import json
import time
import sys
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from src.utils.config import get_settings
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from ..utils.config import get_settings


class SearchClient:
    """搜索API客户端"""
    
    def __init__(self, base_url: Optional[str] = None):
        settings = get_settings()
        self.base_url = base_url or f"http://{settings.api_host}:{settings.api_port}"
    
    def search(
        self, 
        query: str, 
        max_results: int = 5, 
        include_answer: bool = True, 
        use_react_mode: bool = True
    ) -> Dict:
        """调用搜索API"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/search",
                json={
                    "query": query,
                    "max_results": max_results,
                    "include_answer": include_answer,
                    "use_react_mode": use_react_mode
                },
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                return {
                    "success": False,
                    "error": error_data.get("error_message", f"API调用失败: {response.status_code}")
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"网络请求失败: {str(e)}"
            }
    
    def health_check(self) -> Dict:
        """健康检查"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            return response.json() if response.status_code == 200 else {"status": "unhealthy"}
        except Exception:
            return {"status": "unreachable"}


def format_search_results(results: List[Dict]) -> str:
    """格式化搜索结果"""
    if not results:
        return "暂无搜索结果"
    
    formatted = []
    for i, result in enumerate(results, 1):
        formatted.append(f"""
### {i}. {result.get('title', '无标题')}
**链接**: [{result.get('url', '#')}]({result.get('url', '#')})
**相关性**: {result.get('score', 0):.2f}
**摘要**: {result.get('content', '无摘要')}
""")
    
    return "\n".join(formatted)


def search_interface(
    query: str, 
    max_results: int, 
    include_answer: bool, 
    use_react_mode: bool
) -> Tuple[str, str, str, str]:
    """搜索界面处理函数"""
    if not query.strip():
        return "请输入搜索查询", "", "", ""
    
    client = SearchClient()
    
    # 显示搜索状态
    status_msg = f"🔍 正在搜索: {query}..."
    
    # 执行搜索
    start_time = time.time()
    result = client.search(query, max_results, include_answer, use_react_mode)
    end_time = time.time()
    
    if not result.get("success", True):
        error_msg = f"❌ 搜索失败: {result.get('error', '未知错误')}"
        return error_msg, "", "", ""
    
    # 格式化结果
    answer = result.get("answer", "未生成答案")
    results = result.get("results", [])
    formatted_results = format_search_results(results)
    
    # 生成统计信息
    execution_time = result.get("execution_time_ms", int((end_time - start_time) * 1000))
    total_results = result.get("total_results", len(results))
    session_id = result.get("session_id", "unknown")
    
    stats = f"""
**搜索统计**
- 查询: {query}
- 结果数量: {total_results}
- 执行时间: {execution_time}ms
- 会话ID: {session_id}
- 搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    success_msg = f"✅ 搜索完成，找到 {total_results} 个结果"
    
    return success_msg, answer, formatted_results, stats


def create_app() -> gr.Blocks:
    """创建Gradio应用"""
    
    # 自定义CSS样式
    custom_css = """
    .gradio-container {
        max-width: 1200px !important;
        margin: auto !important;
    }
    .search-header {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    .result-box {
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        background: #f8f9fa;
    }
    """
    
    with gr.Blocks(
        title="🔍 智能搜索系统",
        theme=gr.themes.Soft(),
        css=custom_css
    ) as app:
        
        # 标题和描述
        gr.HTML("""
        <div class="search-header">
            <h1>🔍 智能搜索系统</h1>
            <p>基于ReAct模式的AI驱动搜索引擎，提供智能答案和精准结果</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                # 搜索输入区域
                with gr.Group():
                    gr.Markdown("### 🔍 搜索查询")
                    query_input = gr.Textbox(
                        label="输入您的问题",
                        placeholder="例如: 什么是人工智能？最新的AI发展趋势是什么？",
                        lines=2,
                        max_lines=5
                    )
                    
                    with gr.Row():
                        search_btn = gr.Button("🚀 开始搜索", variant="primary", scale=2)
                        clear_btn = gr.Button("🗑️ 清空", scale=1)
            
            with gr.Column(scale=1):
                # 搜索设置
                with gr.Group():
                    gr.Markdown("### ⚙️ 搜索设置")
                    max_results = gr.Slider(
                        minimum=1, maximum=20, value=5, step=1,
                        label="最大结果数量"
                    )
                    include_answer = gr.Checkbox(
                        value=True, label="生成AI答案"
                    )
                    use_react_mode = gr.Checkbox(
                        value=True, label="使用ReAct模式"
                    )
        
        # 搜索状态显示
        status_output = gr.Textbox(
            label="搜索状态",
            interactive=False,
            show_label=True
        )
        
        # 结果显示区域
        with gr.Row():
            with gr.Column(scale=1):
                # AI答案
                with gr.Group():
                    gr.Markdown("### 🤖 AI智能答案")
                    answer_output = gr.Markdown(
                        value="等待搜索结果...",
                        elem_classes=["result-box"]
                    )
            
            with gr.Column(scale=1):
                # 搜索统计
                with gr.Group():
                    gr.Markdown("### 📊 搜索统计")
                    stats_output = gr.Markdown(
                        value="等待搜索结果...",
                        elem_classes=["result-box"]
                    )
        
        # 详细搜索结果
        with gr.Group():
            gr.Markdown("### 📋 详细搜索结果")
            results_output = gr.Markdown(
                value="等待搜索结果...",
                elem_classes=["result-box"]
            )
        
        # 系统状态
        with gr.Accordion("🔧 系统状态", open=False):
            def check_system_status():
                client = SearchClient()
                health = client.health_check()
                status = health.get("status", "unknown")
                
                if status == "healthy":
                    return "✅ 系统运行正常"
                elif status == "degraded":
                    return "⚠️ 系统部分功能异常"
                else:
                    return "❌ 系统不可用"
            
            system_status = gr.Textbox(
                value=check_system_status(),
                label="系统健康状态",
                interactive=False
            )
            
            refresh_status_btn = gr.Button("🔄 刷新状态")
            refresh_status_btn.click(
                fn=check_system_status,
                outputs=system_status
            )
        
        # 事件绑定
        search_btn.click(
            fn=search_interface,
            inputs=[query_input, max_results, include_answer, use_react_mode],
            outputs=[status_output, answer_output, results_output, stats_output]
        )
        
        # 回车键搜索
        query_input.submit(
            fn=search_interface,
            inputs=[query_input, max_results, include_answer, use_react_mode],
            outputs=[status_output, answer_output, results_output, stats_output]
        )
        
        # 清空按钮
        clear_btn.click(
            fn=lambda: ("", "等待搜索结果...", "等待搜索结果...", "等待搜索结果...", ""),
            outputs=[query_input, answer_output, results_output, stats_output, status_output]
        )
        
        # 页脚信息
        gr.HTML("""
        <div style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 30px;">
            <p>🔍 智能搜索系统 | 基于ReAct模式 | 由FastAPI + Gradio驱动</p>
            <p>💡 提示：支持中英文搜索，可以询问任何问题获得智能答案</p>
        </div>
        """)
    
    return app


def main():
    """启动前端应用"""
    app = create_app()
    settings = get_settings()

    print(f"🎨 启动Gradio前端服务...")
    print(f"   地址: http://{settings.frontend_host}:{settings.frontend_port}")

    app.launch(
        server_name=settings.frontend_host,
        server_port=settings.frontend_port,
        share=False,
        debug=settings.debug
    )


if __name__ == "__main__":
    main()
