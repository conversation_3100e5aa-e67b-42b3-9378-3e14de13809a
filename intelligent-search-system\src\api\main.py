"""
FastAPI主应用

智能搜索系统的API服务入口。
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import time
import uuid

from .routes import search_router, health_router
from .models.response import ErrorResponse
from ..utils.config import get_settings
from ..utils.exceptions import SearchSystemError
from .. import __version__

# 获取配置
settings = get_settings()

# 创建FastAPI应用
app = FastAPI(
    title="智能搜索API",
    description="基于ReAct模式的智能搜索服务，集成双层日志系统和Gradio前端",
    version=__version__,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
)

# 添加中间件 - 允许所有来源以解决跨域问题
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 临时允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 添加可信主机中间件（生产环境安全）
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts_list
    )


# 请求ID中间件
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    """为每个请求添加唯一ID"""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# 全局异常处理器
@app.exception_handler(SearchSystemError)
async def search_system_exception_handler(request: Request, exc: SearchSystemError):
    """处理搜索系统异常"""
    error_response = ErrorResponse(
        error_code=exc.error_code or "SEARCH_SYSTEM_ERROR",
        error_message=str(exc),
        details=exc.details if hasattr(exc, 'details') else None,
        request_id=getattr(request.state, 'request_id', None)
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.dict()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    error_response = ErrorResponse(
        error_code="HTTP_ERROR",
        error_message=exc.detail,
        details={"status_code": exc.status_code},
        request_id=getattr(request.state, 'request_id', None)
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理通用异常"""
    error_response = ErrorResponse(
        error_code="INTERNAL_ERROR",
        error_message="内部服务器错误",
        details={"exception_type": type(exc).__name__} if settings.debug else None,
        request_id=getattr(request.state, 'request_id', None)
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.dict()
    )


# 注册路由
app.include_router(search_router)
app.include_router(health_router)


# 根路径
@app.get("/", tags=["root"])
async def root():
    """API根路径"""
    return {
        "name": "智能搜索API",
        "version": __version__,
        "description": "基于ReAct模式的智能搜索服务",
        "docs_url": "/docs" if settings.debug else None,
        "health_url": "/api/v1/health"
    }


# 自定义OpenAPI文档
def custom_openapi():
    """自定义OpenAPI文档"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="智能搜索API",
        version=__version__,
        description="""
        ## 智能搜索系统API
        
        基于ReAct（推理-行动）模式的智能搜索服务，提供：
        
        - **智能搜索**: 使用Tavily搜索引擎进行实时网络搜索
        - **AI答案生成**: 基于搜索结果生成智能答案
        - **双层日志**: 用户友好界面 + 开发者详细日志
        - **高性能**: 异步处理，支持并发请求
        - **安全可靠**: 完整的错误处理和监控
        
        ### 认证
        
        目前API不需要认证，但建议在生产环境中添加API密钥认证。
        
        ### 限流
        
        为保证服务质量，API实施了合理的限流策略。
        
        ### 支持
        
        如有问题，请查看健康检查接口或联系技术支持。
        """,
        routes=app.routes,
    )
    
    # 添加自定义标签
    openapi_schema["tags"] = [
        {
            "name": "search",
            "description": "搜索相关接口"
        },
        {
            "name": "health", 
            "description": "健康检查接口"
        },
        {
            "name": "root",
            "description": "根路径接口"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print("智能搜索API启动成功")
    print(f"版本: {__version__}")
    print(f"文档地址: http://{settings.api_host}:{settings.api_port}/docs")
    print(f"健康检查: http://{settings.api_host}:{settings.api_port}/api/v1/health")


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print("👋 智能搜索API正在关闭...")


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
