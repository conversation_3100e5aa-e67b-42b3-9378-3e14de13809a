#!/usr/bin/env python3
"""
测试网络连接
"""

import socket
import time
import requests

def test_socket_connection(host, port):
    """测试socket连接"""
    print(f"测试socket连接到 {host}:{port}")
    try:
        with socket.create_connection((host, port), timeout=5):
            print(f"✅ Socket连接成功: {host}:{port}")
            return True
    except Exception as e:
        print(f"❌ Socket连接失败: {e}")
        return False

def test_http_request(url):
    """测试HTTP请求"""
    print(f"测试HTTP请求到 {url}")
    try:
        response = requests.get(url, timeout=5)
        print(f"✅ HTTP请求成功: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ HTTP请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("网络连接测试")
    print("=" * 50)
    
    # 测试不同的主机和端口组合
    hosts_to_test = [
        ("127.0.0.1", 8000),
        ("localhost", 8000),
        ("0.0.0.0", 8000),
    ]
    
    for host, port in hosts_to_test:
        test_socket_connection(host, port)
        print()
    
    # 测试HTTP请求
    urls_to_test = [
        "http://127.0.0.1:8000/api/v1/health",
        "http://localhost:8000/api/v1/health",
    ]
    
    for url in urls_to_test:
        test_http_request(url)
        print()

if __name__ == "__main__":
    main()
