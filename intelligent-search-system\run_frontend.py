#!/usr/bin/env python3
"""
前端启动脚本 - 修复版本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """启动前端服务"""
    try:
        print("🎨 启动Gradio前端服务...")
        
        # 导入前端应用
        from src.frontend.app import create_app
        from src.utils.config import get_settings
        
        # 创建应用
        app = create_app()
        settings = get_settings()
        
        print(f"   地址: http://{settings.frontend_host}:{settings.frontend_port}")
        
        # 启动前端 - 使用默认配置避免网络问题
        app.launch(
            server_name="127.0.0.1",
            server_port=7862,
            share=False,
            debug=False,
            show_error=True,
            quiet=False
        )
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
