"""
pytest配置文件

定义测试fixtures和全局配置。
"""

import pytest
import asyncio
from typing import Generator
from fastapi.testclient import TestClient
from src.api.main import app
from src.utils.config import get_settings


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> TestClient:
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def settings():
    """获取测试配置"""
    return get_settings()


@pytest.fixture
def sample_search_query():
    """示例搜索查询"""
    return "什么是人工智能？"


@pytest.fixture
def sample_search_request():
    """示例搜索请求"""
    return {
        "query": "什么是人工智能？",
        "max_results": 5,
        "include_answer": True,
        "use_react_mode": True
    }
