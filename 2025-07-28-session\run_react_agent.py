#!/usr/bin/env python3
"""
快速启动ReAct Agent的脚本
"""

import os
import sys
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查.env文件
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("❌ 未找到.env文件")
            print("💡 请复制.env.example为.env并配置API密钥")
            print("   cp .env.example .env")
            return False
        else:
            print("❌ 未找到环境配置文件")
            return False
    
    # 检查必要的包
    try:
        import openai
        import requests
        from dotenv import load_dotenv
        print("✅ 依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("💡 请安装依赖: pip install openai python-dotenv requests")
        return False
    
    # 检查API密钥
    load_dotenv()
    openai_key = os.getenv("OPENAI_API_KEY")
    tavily_key = os.getenv("TAVILY_API_KEY")
    
    if not openai_key or openai_key == "your_openai_api_key_here":
        print("❌ 未配置OPENAI_API_KEY")
        return False
    
    if not tavily_key or tavily_key == "your_tavily_api_key_here":
        print("❌ 未配置TAVILY_API_KEY")
        return False
    
    print("✅ API密钥配置检查通过")
    return True

def main():
    """主函数"""
    print("🚀 ReAct Agent 启动器")
    print("=" * 50)
    
    if not check_environment():
        print("\n❌ 环境检查失败，请修复上述问题后重试")
        sys.exit(1)
    
    print("\n✅ 环境检查通过，启动ReAct Agent...")
    print("=" * 50)
    
    # 导入并运行主程序
    try:
        from function_calling_with_planning import main as react_main
        import asyncio
        asyncio.run(react_main())
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
