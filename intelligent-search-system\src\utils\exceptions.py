"""
自定义异常模块

定义应用程序特定的异常类型。
"""

from typing import Optional, Dict, Any


class SearchSystemError(Exception):
    """搜索系统基础异常"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ConfigurationError(SearchSystemError):
    """配置错误"""
    pass


class APIKeyError(SearchSystemError):
    """API密钥错误"""
    pass


class SearchEngineError(SearchSystemError):
    """搜索引擎错误"""
    pass


class PlanningError(SearchSystemError):
    """规划引擎错误"""
    pass


class AgentError(SearchSystemError):
    """Agent执行错误"""
    pass


class ValidationError(SearchSystemError):
    """数据验证错误"""
    pass


class TimeoutError(SearchSystemError):
    """超时错误"""
    pass


class RateLimitError(SearchSystemError):
    """限流错误"""
    pass


class ExternalServiceError(SearchSystemError):
    """外部服务错误"""
    pass
