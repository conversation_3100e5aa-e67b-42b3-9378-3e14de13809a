"""
API测试

测试FastAPI接口的功能。
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, patch
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi.testclient import TestClient
from src.api.main import app
from src.api.models.request import SearchRequest
from src.api.models.response import SearchResponse


class TestAPIEndpoints:
    """API端点测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.client = TestClient(app)
    
    def test_root_endpoint(self):
        """测试根端点"""
        response = self.client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "name" in data
        assert "version" in data
    
    def test_health_check(self):
        """测试健康检查"""
        response = self.client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "timestamp" in data
    
    def test_detailed_health_check(self):
        """测试详细健康检查"""
        response = self.client.post(
            "/api/v1/health/detailed",
            json={"check_dependencies": False}
        )
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "uptime_seconds" in data
    
    def test_readiness_check(self):
        """测试就绪检查"""
        response = self.client.get("/api/v1/health/ready")
        # 可能因为API密钥未配置而失败，这是正常的
        assert response.status_code in [200, 503]
    
    def test_liveness_check(self):
        """测试存活检查"""
        response = self.client.get("/api/v1/health/live")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "alive"
    
    def test_search_request_validation(self):
        """测试搜索请求验证"""
        # 测试空查询
        response = self.client.post(
            "/api/v1/search",
            json={"query": ""}
        )
        assert response.status_code == 422  # 验证错误
        
        # 测试无效参数
        response = self.client.post(
            "/api/v1/search",
            json={"query": "test", "max_results": 0}
        )
        assert response.status_code == 422  # 验证错误
    
    @patch('src.core.search.SearchEngine.search')
    def test_search_endpoint_success(self, mock_search):
        """测试搜索端点成功情况"""
        # 模拟搜索结果
        mock_result = Mock()
        mock_result.query = "test query"
        mock_result.answer = "test answer"
        mock_result.results = []
        mock_result.total_results = 0
        mock_search.return_value = mock_result
        
        response = self.client.post(
            "/api/v1/search",
            json={
                "query": "test query",
                "max_results": 5,
                "include_answer": True,
                "use_react_mode": False
            }
        )
        
        # 如果API密钥未配置，可能返回401
        if response.status_code == 401:
            pytest.skip("API密钥未配置，跳过搜索测试")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert data["query"] == "test query"
    
    def test_search_status_endpoint(self):
        """测试搜索状态端点"""
        response = self.client.get("/api/v1/search/status/test_session")
        assert response.status_code in [200, 404]  # 可能找不到会话
    
    def test_cors_headers(self):
        """测试CORS头"""
        response = self.client.options("/api/v1/health")
        assert response.status_code == 200
        # 检查CORS头是否存在
        assert "access-control-allow-origin" in response.headers
    
    def test_request_id_header(self):
        """测试请求ID头"""
        response = self.client.get("/api/v1/health")
        assert "X-Request-ID" in response.headers
        assert "X-Process-Time" in response.headers
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试不存在的端点
        response = self.client.get("/api/v1/nonexistent")
        assert response.status_code == 404
        
        # 测试方法不允许
        response = self.client.delete("/api/v1/health")
        assert response.status_code == 405


class TestDataModels:
    """数据模型测试"""
    
    def test_search_request_model(self):
        """测试搜索请求模型"""
        # 有效请求
        request = SearchRequest(
            query="test query",
            max_results=5,
            include_answer=True,
            use_react_mode=True
        )
        assert request.query == "test query"
        assert request.max_results == 5
        
        # 测试默认值
        request = SearchRequest(query="test")
        assert request.max_results == 5
        assert request.include_answer == True
        assert request.use_react_mode == True
        
        # 测试验证
        with pytest.raises(ValueError):
            SearchRequest(query="")  # 空查询应该失败
    
    def test_search_response_model(self):
        """测试搜索响应模型"""
        response = SearchResponse(
            success=True,
            query="test query",
            answer="test answer",
            results=[],
            total_results=0,
            execution_time_ms=1000,
            session_id="test_session"
        )
        
        assert response.success == True
        assert response.query == "test query"
        assert response.total_results == 0
        assert response.execution_time_ms == 1000


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
