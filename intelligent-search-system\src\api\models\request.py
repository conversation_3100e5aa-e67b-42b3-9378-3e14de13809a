"""
API请求模型

定义API请求的数据结构。
"""

from typing import Optional
from pydantic import BaseModel, Field, validator


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询字符串", min_length=1, max_length=1000)
    max_results: Optional[int] = Field(
        default=5, 
        description="最大结果数量", 
        ge=1, 
        le=20
    )
    include_answer: Optional[bool] = Field(
        default=True, 
        description="是否包含AI生成的答案"
    )
    use_react_mode: Optional[bool] = Field(
        default=True, 
        description="是否使用ReAct模式"
    )
    
    @validator('query')
    def validate_query(cls, v):
        """验证查询字符串"""
        if not v or not v.strip():
            raise ValueError('查询字符串不能为空')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "query": "什么是人工智能？",
                "max_results": 5,
                "include_answer": True,
                "use_react_mode": True
            }
        }


class HealthCheckRequest(BaseModel):
    """健康检查请求模型"""
    check_dependencies: Optional[bool] = Field(
        default=False,
        description="是否检查依赖服务"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "check_dependencies": False
            }
        }
