"""
搜索引擎模块

提供基于Tavily API的智能搜索功能。
"""

import json
import requests
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from ..utils.config import get_settings
from ..utils.logging import DualLogger
from ..utils.exceptions import SearchEngineError, APIKeyError, ExternalServiceError


@dataclass
class SearchResult:
    """搜索结果数据类"""
    title: str
    url: str
    content: str
    score: float = 0.0


@dataclass
class SearchResponse:
    """搜索响应数据类"""
    query: str
    answer: str
    results: List[SearchResult]
    total_results: int = 0


class SearchEngine:
    """搜索引擎类"""
    
    def __init__(self, logger: Optional[DualLogger] = None):
        self.settings = get_settings()
        self.logger = logger or DualLogger()
        
        # 验证API密钥
        if not self.settings.tavily_api_key:
            raise APIKeyError("Tavily API密钥未配置")
    
    async def search(
        self, 
        query: str, 
        max_results: int = None, 
        include_answer: bool = True
    ) -> SearchResponse:
        """
        执行搜索查询
        
        Args:
            query: 搜索查询字符串
            max_results: 最大结果数量
            include_answer: 是否包含AI生成的答案
            
        Returns:
            SearchResponse: 搜索响应对象
            
        Raises:
            SearchEngineError: 搜索执行失败
        """
        if max_results is None:
            max_results = self.settings.default_max_results
            
        # 开始日志记录
        step_id = self.logger.start_step(f"Tavily搜索: {query}", {
            "query": query,
            "max_results": max_results,
            "include_answer": include_answer
        })
        
        try:
            # API配置
            api_url = "https://api.tavily.com/search"
            payload = {
                "api_key": self.settings.tavily_api_key,
                "query": query,
                "max_results": max_results,
                "include_answer": include_answer,
                "search_depth": "advanced"
            }
            
            # 发送HTTP请求
            response = requests.post(
                api_url, 
                json=payload, 
                timeout=self.settings.default_timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 解析搜索结果
                results = []
                for result_data in data.get("results", []):
                    result = SearchResult(
                        title=result_data.get("title", ""),
                        url=result_data.get("url", ""),
                        content=result_data.get("content", ""),
                        score=result_data.get("score", 0.0)
                    )
                    results.append(result)
                
                # 创建响应对象
                search_response = SearchResponse(
                    query=data.get("query", query),
                    answer=data.get("answer", ""),
                    results=results,
                    total_results=len(results)
                )
                
                # 完成日志记录
                self.logger.complete_step(step_id, {
                    "results_count": len(results),
                    "has_answer": bool(search_response.answer),
                    "api_response_code": response.status_code
                })
                
                return search_response
                
            else:
                error_msg = f"Tavily API调用失败: HTTP {response.status_code}"
                self.logger.complete_step(step_id, error=f"{error_msg}: {response.text}")
                raise ExternalServiceError(
                    error_msg,
                    error_code="TAVILY_API_ERROR",
                    details={"status_code": response.status_code, "response": response.text}
                )
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.complete_step(step_id, error=error_msg)
            raise ExternalServiceError(
                error_msg,
                error_code="NETWORK_ERROR",
                details={"exception": str(e)}
            )
        except Exception as e:
            error_msg = f"搜索执行失败: {str(e)}"
            self.logger.complete_step(step_id, error=error_msg)
            raise SearchEngineError(
                error_msg,
                error_code="SEARCH_ERROR",
                details={"exception": str(e)}
            )
    
    def search_to_json(
        self, 
        query: str, 
        max_results: int = None, 
        include_answer: bool = True
    ) -> str:
        """
        执行搜索并返回JSON字符串（同步版本）
        
        Args:
            query: 搜索查询字符串
            max_results: 最大结果数量
            include_answer: 是否包含AI生成的答案
            
        Returns:
            str: 搜索结果的JSON字符串
        """
        try:
            # 同步版本的搜索实现
            if max_results is None:
                max_results = self.settings.default_max_results
                
            step_id = self.logger.start_step(f"Tavily搜索: {query}", {
                "query": query,
                "max_results": max_results,
                "include_answer": include_answer
            })
            
            api_url = "https://api.tavily.com/search"
            payload = {
                "api_key": self.settings.tavily_api_key,
                "query": query,
                "max_results": max_results,
                "include_answer": include_answer,
                "search_depth": "advanced"
            }
            
            response = requests.post(
                api_url, 
                json=payload, 
                timeout=self.settings.default_timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 格式化结果
                formatted_results = {
                    "query": data.get("query", query),
                    "answer": data.get("answer", ""),
                    "results": []
                }
                
                for result in data.get("results", []):
                    formatted_results["results"].append({
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "content": result.get("content", ""),
                        "score": result.get("score", 0.0)
                    })
                
                self.logger.complete_step(step_id, {
                    "results_count": len(formatted_results["results"]),
                    "has_answer": bool(formatted_results["answer"]),
                    "api_response_code": response.status_code
                })
                
                return json.dumps(formatted_results, ensure_ascii=False)
            else:
                error_result = {
                    "error": f"Tavily API调用失败: HTTP {response.status_code}",
                    "details": response.text,
                    "results": []
                }
                self.logger.complete_step(step_id, error=f"HTTP {response.status_code}: {response.text}")
                return json.dumps(error_result, ensure_ascii=False)
                
        except Exception as e:
            error_result = {
                "error": f"搜索执行失败: {str(e)}",
                "results": []
            }
            if 'step_id' in locals():
                self.logger.complete_step(step_id, error=str(e))
            return json.dumps(error_result, ensure_ascii=False)
