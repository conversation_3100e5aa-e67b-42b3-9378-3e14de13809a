"""
配置管理模块

统一管理应用程序的配置项，支持环境变量和配置文件。
"""

import os
from typing import Optional, List
from functools import lru_cache
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    # 兼容旧版本 pydantic
    from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings(BaseSettings):
    """应用程序配置"""
    
    # 应用基本信息
    app_name: str = Field(default="Intelligent Search System", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # OpenAI配置
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    openai_model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    
    # Tavily搜索API配置
    tavily_api_key: str = Field(..., env="TAVILY_API_KEY")
    
    # 服务器配置
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    frontend_host: str = Field(default="0.0.0.0", env="FRONTEND_HOST")
    frontend_port: int = Field(default=7860, env="FRONTEND_PORT")
    
    # 数据库配置（可选）
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # Redis配置（可选）
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # 日志配置
    log_dir: str = Field(default="logs", env="LOG_DIR")
    log_rotation: str = Field(default="1 day", env="LOG_ROTATION")
    log_retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    # 搜索配置
    default_max_results: int = Field(default=5, env="DEFAULT_MAX_RESULTS")
    default_timeout: int = Field(default=30, env="DEFAULT_TIMEOUT")
    enable_react_mode: bool = Field(default=True, env="ENABLE_REACT_MODE")
    
    # 安全配置
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    allowed_hosts: str = Field(default="localhost,127.0.0.1", env="ALLOWED_HOSTS")

    # CORS配置
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:7860",
        env="CORS_ORIGINS"
    )
    cors_methods: str = Field(
        default="GET,POST,PUT,DELETE",
        env="CORS_METHODS"
    )
    cors_headers: str = Field(default="*", env="CORS_HEADERS")

    # 解析列表字段的属性
    @property
    def allowed_hosts_list(self) -> List[str]:
        return [host.strip() for host in self.allowed_hosts.split(",")]

    @property
    def cors_origins_list(self) -> List[str]:
        return [origin.strip() for origin in self.cors_origins.split(",")]

    @property
    def cors_methods_list(self) -> List[str]:
        return [method.strip() for method in self.cors_methods.split(",")]

    @property
    def cors_headers_list(self) -> List[str]:
        return [header.strip() for header in self.cors_headers.split(",")]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str) -> any:
            """解析环境变量，支持列表类型"""
            if field_name in ['allowed_hosts', 'cors_origins', 'cors_methods', 'cors_headers']:
                return [item.strip() for item in raw_val.split(',')]
            return cls.json_loads(raw_val)


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()


# 便捷访问
settings = get_settings()
