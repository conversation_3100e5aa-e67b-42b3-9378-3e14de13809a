#!/usr/bin/env python3
"""
文档处理器：使用Marker提取文档内容，并用多模态模型增强图片描述
支持PDF和DOCX文件，生成可直接用于Chat模型分析的增强Markdown
"""

import os
import base64
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json

# Marker相关导入
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered

# OpenAI Vision API
from openai import OpenAI

class DocumentProcessor:
    def __init__(self, openai_api_key: str = None):
        """
        初始化文档处理器
        
        Args:
            openai_api_key: OpenAI API密钥，如果不提供则从环境变量获取
        """
        # 初始化Marker
        self.marker_converter = PdfConverter(
            artifact_dict=create_model_dict(),
        )
        
        # 初始化OpenAI客户端（用于图片分析）
        self.openai_client = OpenAI(
            api_key=openai_api_key or os.environ.get("OPENAI_API_KEY")
        )
    
    def process_document(
        self, 
        file_path: str, 
        output_dir: str = "./output",
        analyze_images: bool = True,
        image_analysis_prompt: str = "请详细描述这张图片的内容，包括文字、图表、关键信息等。用中文回答。"
    ) -> Dict:
        """
        处理文档：提取内容并增强图片描述
        
        Args:
            file_path: 文档文件路径
            output_dir: 输出目录
            analyze_images: 是否使用多模态模型分析图片
            image_analysis_prompt: 图片分析提示词
            
        Returns:
            包含处理结果的字典
        """
        file_path = Path(file_path)
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        print(f"🔄 开始处理文档: {file_path.name}")
        
        # 1. 使用Marker提取内容
        print("📄 使用Marker提取文档内容...")
        rendered = self.marker_converter(str(file_path))
        text, _, images = text_from_rendered(rendered)
        
        print(f"✅ 提取完成: {len(images)} 张图片")
        
        # 2. 保存原始图片
        image_files = self._save_images(images, output_dir, file_path.stem)
        
        # 3. 分析图片（如果启用）
        image_descriptions = {}
        if analyze_images and images:
            print("🔍 开始分析图片内容...")
            image_descriptions = self._analyze_images(
                images, image_analysis_prompt
            )
        
        # 4. 生成增强的Markdown
        enhanced_markdown = self._enhance_markdown_with_descriptions(
            text, image_descriptions
        )
        
        # 5. 保存结果
        results = self._save_results(
            output_dir, file_path.stem, enhanced_markdown, 
            image_descriptions, image_files
        )
        
        print(f"🎉 处理完成! 输出目录: {output_dir}")
        return results
    
    def _save_images(self, images: Dict, output_dir: Path, base_name: str) -> Dict[str, str]:
        """保存图片文件"""
        image_dir = output_dir / f"{base_name}_images"
        image_dir.mkdir(exist_ok=True)
        
        image_files = {}
        for img_id, img_data in images.items():
            # 解码base64图片数据
            img_bytes = base64.b64decode(img_data)
            
            # 保存图片文件
            img_file = image_dir / f"{img_id}.png"
            with open(img_file, 'wb') as f:
                f.write(img_bytes)
            
            image_files[img_id] = str(img_file)
            
        return image_files
    
    def _analyze_images(self, images: Dict, prompt: str) -> Dict[str, str]:
        """使用OpenAI Vision API分析图片"""
        descriptions = {}
        
        for i, (img_id, img_data) in enumerate(images.items(), 1):
            print(f"  分析图片 {i}/{len(images)}: {img_id}")
            
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",  # 使用支持视觉的模型
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{img_data}"
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens=500
                )
                
                description = response.choices[0].message.content
                descriptions[img_id] = description
                print(f"    ✅ 分析完成")
                
            except Exception as e:
                print(f"    ❌ 分析失败: {e}")
                descriptions[img_id] = f"[图片分析失败: {str(e)}]"
        
        return descriptions
    
    def _enhance_markdown_with_descriptions(
        self, 
        markdown_text: str, 
        descriptions: Dict[str, str]
    ) -> str:
        """在Markdown中的图片引用下方添加描述"""
        
        if not descriptions:
            return markdown_text
        
        # 查找图片引用的正则表达式
        # 匹配 ![alt](image_id) 或 <img src="image_id" ...> 格式
        img_pattern = r'(!\[.*?\]\([^)]*?([^/)]+)\)|<img[^>]*src=["\']([^"\']*?)["\'][^>]*>)'
        
        def replace_image(match):
            full_match = match.group(0)
            
            # 提取图片ID
            img_id = None
            if match.group(2):  # Markdown格式
                img_id = match.group(2)
            elif match.group(3):  # HTML格式
                img_id = Path(match.group(3)).stem
            
            # 如果找到对应的描述，添加到图片下方
            if img_id and img_id in descriptions:
                description = descriptions[img_id]
                return f"{full_match}\n\n**图片描述：** {description}\n"
            
            return full_match
        
        enhanced_text = re.sub(img_pattern, replace_image, markdown_text)
        return enhanced_text
    
    def _save_results(
        self, 
        output_dir: Path, 
        base_name: str, 
        enhanced_markdown: str,
        descriptions: Dict[str, str],
        image_files: Dict[str, str]
    ) -> Dict:
        """保存处理结果"""
        
        # 保存增强的Markdown
        markdown_file = output_dir / f"{base_name}_enhanced.md"
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(enhanced_markdown)
        
        # 保存图片描述JSON
        descriptions_file = output_dir / f"{base_name}_descriptions.json"
        with open(descriptions_file, 'w', encoding='utf-8') as f:
            json.dump(descriptions, f, ensure_ascii=False, indent=2)
        
        # 保存处理报告
        report = {
            "source_file": base_name,
            "enhanced_markdown": str(markdown_file),
            "image_descriptions": str(descriptions_file),
            "image_files": image_files,
            "stats": {
                "total_images": len(image_files),
                "analyzed_images": len(descriptions),
                "markdown_length": len(enhanced_markdown)
            }
        }
        
        report_file = output_dir / f"{base_name}_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report


def main():
    """示例用法"""
    processor = DocumentProcessor()
    
    # 处理文档
    result = processor.process_document(
        file_path="example.pdf",  # 替换为你的文档路径
        output_dir="./processed_docs",
        analyze_images=True,
        image_analysis_prompt="请详细描述这张图片的内容，重点关注文字信息、图表数据、关键概念等。用中文回答。"
    )
    
    print("\n📊 处理结果:")
    print(f"- 增强Markdown: {result['enhanced_markdown']}")
    print(f"- 图片数量: {result['stats']['total_images']}")
    print(f"- 分析图片: {result['stats']['analyzed_images']}")


if __name__ == "__main__":
    main()
