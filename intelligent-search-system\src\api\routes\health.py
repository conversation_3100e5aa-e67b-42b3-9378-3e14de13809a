"""
健康检查API路由

提供服务健康状态检查接口。
"""

import time
from datetime import datetime
from fastapi import APIRouter, HTTPException
import requests

from ..models.request import HealthCheckRequest
from ..models.response import HealthCheckResponse
from ...utils.config import get_settings
from ... import __version__

router = APIRouter(prefix="/api/v1", tags=["health"])

# 服务启动时间
_start_time = time.time()


@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    基础健康检查
    
    返回服务的基本健康状态信息。
    """
    uptime_seconds = int(time.time() - _start_time)
    
    return HealthCheckResponse(
        status="healthy",
        timestamp=datetime.now(),
        version=__version__,
        uptime_seconds=uptime_seconds
    )


@router.post("/health/detailed", response_model=HealthCheckResponse)
async def detailed_health_check(request: HealthCheckRequest):
    """
    详细健康检查
    
    检查服务及其依赖的健康状态。
    """
    uptime_seconds = int(time.time() - _start_time)
    dependencies = {}
    
    if request.check_dependencies:
        settings = get_settings()
        
        # 检查OpenAI API
        try:
            # 这里可以添加实际的API连通性检查
            dependencies["openai_api"] = "healthy"
        except Exception:
            dependencies["openai_api"] = "unhealthy"
        
        # 检查Tavily API
        try:
            # 简单的连通性检查
            test_url = "https://api.tavily.com"
            response = requests.get(test_url, timeout=5)
            dependencies["tavily_api"] = "healthy" if response.status_code < 500 else "unhealthy"
        except Exception:
            dependencies["tavily_api"] = "unhealthy"
    
    # 判断整体状态
    overall_status = "healthy"
    if dependencies and any(status == "unhealthy" for status in dependencies.values()):
        overall_status = "degraded"
    
    return HealthCheckResponse(
        status=overall_status,
        timestamp=datetime.now(),
        version=__version__,
        dependencies=dependencies if dependencies else None,
        uptime_seconds=uptime_seconds
    )


@router.get("/health/ready")
async def readiness_check():
    """
    就绪检查
    
    用于Kubernetes等容器编排系统的就绪探针。
    """
    try:
        settings = get_settings()
        
        # 检查必要的配置
        if not settings.openai_api_key:
            raise HTTPException(status_code=503, detail="OpenAI API密钥未配置")
        
        if not settings.tavily_api_key:
            raise HTTPException(status_code=503, detail="Tavily API密钥未配置")
        
        return {"status": "ready"}
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"服务未就绪: {str(e)}")


@router.get("/health/live")
async def liveness_check():
    """
    存活检查
    
    用于Kubernetes等容器编排系统的存活探针。
    """
    return {"status": "alive", "timestamp": datetime.now()}
