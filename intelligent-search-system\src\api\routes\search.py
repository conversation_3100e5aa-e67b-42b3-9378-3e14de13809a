"""
搜索API路由

处理搜索相关的API请求。
"""

import time
import uuid
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse

from ..models.request import SearchRequest
from ..models.response import SearchResponse, SearchResultItem, ErrorResponse
from ...core.agent import ReactAgent
from ...core.search import SearchEngine
from ...utils.logging import DualLogger
from ...utils.exceptions import SearchSystemError, APIKeyError, ExternalServiceError

router = APIRouter(prefix="/api/v1", tags=["search"])


@router.post("/search", response_model=SearchResponse)
async def search_endpoint(request: SearchRequest, background_tasks: BackgroundTasks):
    """
    智能搜索接口
    
    执行基于ReAct模式的智能搜索，返回搜索结果和AI生成的答案。
    """
    start_time = time.time()
    session_id = f"search_{int(time.time())}_{uuid.uuid4().hex[:8]}"
    
    try:
        # 创建日志器
        logger = DualLogger(session_id)
        
        if request.use_react_mode:
            # 使用ReAct Agent进行搜索
            agent = ReactAgent(logger)
            
            # 执行搜索（这里简化处理，实际应该实现完整的ReAct循环）
            search_response = await agent.search_engine.search(
                query=request.query,
                max_results=request.max_results,
                include_answer=request.include_answer
            )
            
            # 转换为API响应格式
            result_items = [
                SearchResultItem(
                    title=result.title,
                    url=result.url,
                    content=result.content,
                    score=result.score
                )
                for result in search_response.results
            ]
            
        else:
            # 直接使用搜索引擎
            search_engine = SearchEngine(logger)
            search_response = await search_engine.search(
                query=request.query,
                max_results=request.max_results,
                include_answer=request.include_answer
            )
            
            result_items = [
                SearchResultItem(
                    title=result.title,
                    url=result.url,
                    content=result.content,
                    score=result.score
                )
                for result in search_response.results
            ]
        
        # 计算执行时间
        execution_time_ms = int((time.time() - start_time) * 1000)
        
        # 构建响应
        response = SearchResponse(
            success=True,
            query=search_response.query,
            answer=search_response.answer,
            results=result_items,
            total_results=search_response.total_results,
            execution_time_ms=execution_time_ms,
            session_id=session_id,
            timestamp=datetime.now()
        )
        
        # 后台任务：清理日志（可选）
        background_tasks.add_task(_cleanup_old_logs)
        
        return response
        
    except APIKeyError as e:
        error_response = ErrorResponse(
            error_code="API_KEY_ERROR",
            error_message=str(e),
            details=e.details if hasattr(e, 'details') else None,
            request_id=session_id
        )
        raise HTTPException(status_code=401, detail=error_response.dict())
        
    except ExternalServiceError as e:
        error_response = ErrorResponse(
            error_code="EXTERNAL_SERVICE_ERROR",
            error_message=str(e),
            details=e.details if hasattr(e, 'details') else None,
            request_id=session_id
        )
        raise HTTPException(status_code=503, detail=error_response.dict())
        
    except SearchSystemError as e:
        error_response = ErrorResponse(
            error_code=e.error_code or "SEARCH_ERROR",
            error_message=str(e),
            details=e.details if hasattr(e, 'details') else None,
            request_id=session_id
        )
        raise HTTPException(status_code=500, detail=error_response.dict())
        
    except Exception as e:
        error_response = ErrorResponse(
            error_code="INTERNAL_ERROR",
            error_message=f"内部服务器错误: {str(e)}",
            details={"exception_type": type(e).__name__},
            request_id=session_id
        )
        raise HTTPException(status_code=500, detail=error_response.dict())


@router.get("/search/status/{session_id}")
async def get_search_status(session_id: str):
    """
    获取搜索状态
    
    查询指定会话的搜索执行状态和进度。
    """
    try:
        # 这里应该实现状态查询逻辑
        # 可以从日志文件或缓存中读取状态信息
        
        return {
            "session_id": session_id,
            "status": "completed",  # running, completed, error
            "progress": "100%",
            "message": "搜索已完成"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=404, 
            detail=f"会话 {session_id} 不存在或已过期"
        )


async def _cleanup_old_logs():
    """清理旧日志文件的后台任务"""
    # 这里可以实现日志清理逻辑
    # 例如删除超过30天的日志文件
    pass
