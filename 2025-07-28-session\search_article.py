#!/usr/bin/env python3
"""
搜索微信文章相关信息
"""

import requests
import json

def search_article_info():
    """搜索文章相关信息"""
    
    # 使用Tavily API搜索
    api_key = "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"
    api_url = "https://api.tavily.com/search"
    
    queries = [
        "wutongci-crawl-mcp 微信公众号文章抓取教程",
        "MCP Cursor 微信文章抓取配置",
        "crawl-mcp-server 使用方法"
    ]
    
    for query in queries:
        print(f"\n🔍 搜索: {query}")
        print("=" * 60)
        
        payload = {
            "api_key": api_key,
            "query": query,
            "max_results": 3,
            "include_answer": True,
            "search_depth": "advanced"
        }
        
        try:
            response = requests.post(api_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # 显示AI答案
                answer = data.get("answer", "")
                if answer:
                    print(f"🤖 AI答案: {answer}")
                
                # 显示搜索结果
                results = data.get("results", [])
                print(f"📄 找到 {len(results)} 个结果:")
                
                for i, result in enumerate(results):
                    print(f"\n📄 结果 {i+1}:")
                    print(f"   标题: {result.get('title', 'N/A')}")
                    print(f"   URL: {result.get('url', 'N/A')}")
                    print(f"   内容: {result.get('content', 'N/A')[:150]}...")
                    
            else:
                print(f"❌ 搜索失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    search_article_info()
