#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能搜索应用启动器
同时启动FastAPI后端和Gradio前端
"""

import os
import sys
import time
import subprocess
import threading
import requests
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        "fastapi",
        "uvicorn",
        "gradio",
        "requests",
        "pydantic"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查API密钥
    tavily_key = os.getenv("TAVILY_API_KEY")
    if not tavily_key or tavily_key == "your_tavily_api_key_here":
        print("⚠️  未配置TAVILY_API_KEY，将使用默认密钥")
        print("💡 建议在.env文件中配置您的API密钥")
    else:
        print("✅ TAVILY_API_KEY已配置")
    
    # 检查必要文件
    required_files = [
        "backend_api.py",
        "frontend_gradio.py",
        "dual_logging_react_agent.py"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少文件: {file}")
            return False
        print(f"✅ 文件存在: {file}")
    
    return True

def wait_for_api(url: str, timeout: int = 30):
    """等待API服务启动"""
    print(f"⏳ 等待API服务启动: {url}")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ API服务已启动")
                return True
        except:
            pass
        time.sleep(1)
    
    print("❌ API服务启动超时")
    return False

def run_backend():
    """运行后端服务"""
    print("🚀 启动FastAPI后端服务...")
    try:
        subprocess.run([
            sys.executable, "backend_api.py"
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")

def run_frontend():
    """运行前端服务"""
    print("🌐 启动Gradio前端界面...")
    
    # 等待后端服务启动
    if not wait_for_api("http://localhost:8000"):
        print("❌ 后端服务未启动，无法启动前端")
        return
    
    try:
        subprocess.run([
            sys.executable, "frontend_gradio.py"
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 前端服务已停止")
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 智能搜索应用启动器")
    print("🔧 技术栈: FastAPI + Gradio + ReAct + Tavily")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    print("\n✅ 环境检查通过，开始启动服务...")
    print("=" * 60)
    
    try:
        # 创建线程启动后端
        backend_thread = threading.Thread(target=run_backend, daemon=True)
        backend_thread.start()
        
        # 等待一下让后端先启动
        time.sleep(3)
        
        # 启动前端（主线程）
        run_frontend()
        
    except KeyboardInterrupt:
        print("\n🛑 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_usage():
    """显示使用说明"""
    print("""
🔍 智能搜索应用使用说明

📦 安装依赖:
   pip install fastapi uvicorn gradio requests pydantic

🚀 启动应用:
   python run_search_app.py

🌐 访问地址:
   - 前端界面: http://localhost:7860
   - 后端API: http://localhost:8000
   - API文档: http://localhost:8000/docs

⚙️ 环境配置:
   在.env文件中配置TAVILY_API_KEY

🎯 功能特性:
   - ReAct智能搜索模式
   - 双层日志系统
   - 实时进度跟踪
   - 用户友好界面
   - 开发者调试工具

📚 技术架构:
   - 后端: FastAPI + ReAct工作流程
   - 前端: Gradio Web界面
   - 搜索: Tavily API
   - 日志: 双层日志系统
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_usage()
    else:
        main()
