"""
集成测试

测试整个系统的集成功能。
"""

import pytest
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.agent import ReactAgent
from src.core.search import SearchEngine
from src.core.planning import PlanningEngine
from src.utils.config import get_settings
from src.utils.logging import DualLogger


class TestSystemIntegration:
    """系统集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.logger = DualLogger("test_session")
        self.settings = get_settings()
    
    def test_config_loading(self):
        """测试配置加载"""
        assert self.settings is not None
        assert hasattr(self.settings, 'openai_api_key')
        assert hasattr(self.settings, 'tavily_api_key')
    
    def test_logger_creation(self):
        """测试日志器创建"""
        assert self.logger is not None
        assert hasattr(self.logger, 'start_step')
        assert hasattr(self.logger, 'complete_step')
    
    def test_search_engine_creation(self):
        """测试搜索引擎创建"""
        search_engine = SearchEngine(self.logger)
        assert search_engine is not None
        assert hasattr(search_engine, 'search')
    
    def test_planning_engine_creation(self):
        """测试规划引擎创建"""
        planning_engine = PlanningEngine(self.logger)
        assert planning_engine is not None
        assert hasattr(planning_engine, 'think_and_plan')
    
    @pytest.mark.asyncio
    async def test_agent_creation(self):
        """测试代理创建"""
        try:
            agent = ReactAgent(self.logger)
            assert agent is not None
            assert hasattr(agent, 'function_calling')
            assert hasattr(agent, 'execute_tool_call')
        except Exception as e:
            # 如果API密钥未配置，跳过此测试
            if "API密钥未配置" in str(e):
                pytest.skip("API密钥未配置，跳过代理测试")
            else:
                raise
    
    def test_project_structure(self):
        """测试项目结构"""
        # 检查关键文件是否存在
        key_files = [
            "src/__init__.py",
            "src/core/__init__.py",
            "src/core/agent.py",
            "src/core/search.py", 
            "src/core/planning.py",
            "src/utils/__init__.py",
            "src/utils/config.py",
            "src/utils/logging.py",
            "src/utils/exceptions.py",
            "src/api/__init__.py",
            "src/api/main.py",
            "src/frontend/__init__.py",
            "src/frontend/app.py",
            "requirements.txt",
            "pyproject.toml",
            "README.md",
            "Dockerfile",
            "docker-compose.yml"
        ]
        
        for file_path in key_files:
            full_path = project_root / file_path
            assert full_path.exists(), f"关键文件不存在: {file_path}"
    
    def test_imports(self):
        """测试模块导入"""
        try:
            from src import __version__
            from src.utils.config import get_settings
            from src.utils.logging import DualLogger
            from src.utils.exceptions import SearchSystemError
            from src.core.search import SearchEngine
            from src.core.planning import PlanningEngine
            
            assert __version__ is not None
            assert get_settings is not None
            assert DualLogger is not None
            assert SearchSystemError is not None
            assert SearchEngine is not None
            assert PlanningEngine is not None
            
        except ImportError as e:
            pytest.fail(f"模块导入失败: {e}")


def test_environment_setup():
    """测试环境设置"""
    # 检查Python版本
    assert sys.version_info >= (3, 9), "Python版本需要3.9或更高"
    
    # 检查项目根目录
    assert project_root.exists(), "项目根目录不存在"
    assert (project_root / "src").exists(), "src目录不存在"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
