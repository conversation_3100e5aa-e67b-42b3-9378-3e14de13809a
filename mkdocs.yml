site_name: LLM-TT

theme:
  name: "material"
  language: zh
  palette:
    # 切换到亮色
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: blue
      accent: blue
      toggle:
        icon: material/weather-night
        name: 切换到暗色模式
                    
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: blue
      accent: blue
      toggle:
        icon: material/weather-night
        name: 切换到暗色模式

  features:
    - navigation.instant # 现在页面不会跳转,而是类似单页应用,搜索和各种跳转都是在当前页面完成,对美观有很大帮助
    - navigation.tabs # 页面上方的标签页
    - navigation.tracking # 页面滚动时，导航栏高亮当前页面
    - navigation.sections # 使导航栏分块
    - navigation.expand # 默认展开导航
    - navigation.prune # 只渲染当前页面的导航
    - toc.follow # 滚动的时候侧边栏自动跟随
    - navigation.top # 返回顶部按钮
    - search.suggest # 补全建议
    - search.highlight # 搜索结果高亮
    - search.share # 搜索结果分享
    - navigation.footer # 页脚提示下一章
    - content.code.copy # 代码段上的赋值按钮

markdown_extensions:
  - admonition # 警告语法
  - def_list
  - footnotes
  - abbr
  - pymdownx.caret
  - pymdownx.mark
  - pymdownx.tilde
  - md_in_html
  - pymdownx.arithmatex: # latex支持
        generic: true
  - toc:
        permalink: true # 固定标题位置为当前位置
        toc_depth: 3 # 目录深度
  - pymdownx.highlight: # 代码块高亮
        anchor_linenums: true
        linenums: true # 显示行号
        use_pygments: true # 代码高亮
        pygments_lang_class: true
        auto_title: true # 显示编程语言名称
        linenums_style: pymdownx-inline # 行号样式,防止复制的时候复制行号
  - pymdownx.betterem # 强调美化,比如**text**会被美化
  - pymdownx.caret # 上标和下标
  - pymdownx.mark # 上标和下标
  - pymdownx.tilde # 上标和下标
  - pymdownx.keys # 显示按键组合
  - pymdownx.critic
  - pymdownx.details # 可以折叠的代码块 ??? note 可以让警告变成折叠的
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - pymdownx.magiclink # 自动识别链接
  - pymdownx.smartsymbols # 智能符号
  - pymdownx.snippets # 代码段
  - pymdownx.tasklist:
        custom_checkbox: true # 自定义复选框
  - attr_list
  - pymdownx.emoji:
        emoji_index: !!python/name:material.extensions.emoji.twemoji
        emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.superfences: # 代码块中支持Mermaid
        custom_fences: # 支持 Mermaid
            - name: mermaid
              class: mermaid
              format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
        alternate_style: true
        combine_header_slug: true
  - pymdownx.tasklist:
        custom_checkbox: true
        clickable_checkbox: true
  - meta # 支持Markdown文件上方自定义标题标签等
  - tables
    

plugins:
  - mkdocstrings:
      handlers:
        python:
          paths: [src]
          options:
          options:
            docstring_style: google
            show_root_heading: true  # 这个很重要，确保显示根级标题
            show_root_toc_entry: true  # 确保在目录中显示
            backlinks: tree
            parameter_headings: false
            show_symbol_type_toc: true
            show_symbol_type_heading: true
            docstring_section_style: table
            separate_signature: true
            modernize_annotations: true
            show_signature_annotations: true
            
  - include-markdown

nav:
  - Home: index.md
  - MyManus:
    - agent: agent.md